{"name": "data-service", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"prebuild": "rm -rf dist", "build": "esbuild index.ts --bundle --sourcemap --platform=node --target=es2020 --outfile=dist/index.js", "postbuild": "cd dist && zip -r function.zip ."}, "author": "daniel", "license": "ISC", "dependencies": {"@aws-sdk/client-s3": "^3.575.0", "@aws-sdk/client-sns": "^3.609.0", "@aws-sdk/s3-request-presigner": "^3.575.0", "adm-zip": "^0.5.12", "aws-lambda": "^1.0.7", "aws-sdk": "^2.1614.0", "axios": "^1.6.8", "class-validator": "^0.14.1", "csv-parser": "^3.0.0", "date-fns-tz": "^3.2.0", "dotenv": "^16.4.5", "exceljs": "^4.4.0", "moment-timezone": "^0.5.45", "util": "^0.12.5", "xlsx": "^0.18.5", "xlsx-populate": "^1.21.0", "zlib": "^1.0.5"}, "devDependencies": {"@types/node": "^20.12.11", "esbuild": "^0.23.1", "tsconfig-paths": "^4.2.0", "tslib": "^2.6.2"}, "packageManager": "yarn@1.22.21+sha1.1959a18351b811cdeedbd484a8f86c3cc3bbaf72"}