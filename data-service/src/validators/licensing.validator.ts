import { <PERSON><PERSON><PERSON><PERSON>, IsISO8601, <PERSON><PERSON><PERSON><PERSON>, validate, IsMongoId } from "class-validator";
export class LicensingValidator {
  @IsMongoId()
  company_id: string;

  @IsNumber()
  status: number;

  @IsISO8601()
  start_date: string;

  @IsISO8601()
  end_date: string;

  @IsNumber()
  users_allowed: number;

  // @IsNumber()
  // users_in_use: number;

  @IsArray()
  users: Array<Object>;

  constructor(payload: any) {
    this.company_id = payload.company_id;
    this.status = payload.status;
    this.start_date = payload.start_date;
    this.end_date = payload.end_date;
    this.users_allowed = payload.users_allowed;
    // this.users_in_use = payload.users_in_use;
    this.users = payload.users;
  }

  async getErrorMessage() {
    const errors = await validate(this);
    let errorMessages = [];

    errors.forEach((error) => {
      Object.values(error.constraints).forEach((message) => {
        errorMessages.push(message);
      });
    });

    return errorMessages.join(", ");
  }
}
