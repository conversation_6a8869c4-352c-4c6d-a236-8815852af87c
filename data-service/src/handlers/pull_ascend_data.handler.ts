import { configService, formatDateWithTimezone, getToken, httpResponse, request } from "../helpers";
import { DynamoDBService } from "../services/dynamodb.service";
import ascend from "@services/ascend.service";
import moment_tz from "moment-timezone";
import zlib from "zlib";
import { SNSClient, PublishCommand } from "@aws-sdk/client-sns";
const snsClient = new SNSClient({ region: "ap-southeast-1" });
const SNS_TOPIC_ARN = configService.get("SNS_TOPIC_ARN");

const db = new DynamoDBService();
const handler = async (event, context) => {
  try {
    let startOfYesterdaySingapore = moment_tz()
      .tz("Asia/Singapore")
      .subtract(1, "day")
      .startOf("day")
      .toDate()
      .getTime();
    let endOfYesterdaySingapore = moment_tz()
      .tz("Asia/Singapore")
      .subtract(1, "day")
      .endOf("day")
      .toDate()
      .getTime();
    

    let resultPullAscend = { successes: [], fails: [] };
    let processList; 
    if ('Records' in event) {
      const { data } = JSON.parse(event.Records[0].Sns.Message);
      startOfYesterdaySingapore = data.startOfYesterdaySingapore;
      endOfYesterdaySingapore = data.endOfYesterdaySingapore;
      resultPullAscend = data.resultPullAscend;
      processList = data.processList
    } else {
      const result = await request({
        method: "GET",
        url: `${configService.get("CTR_URL")}/vehicles?$limit=2000`,
        token: await getToken(),
      });

      if (!result.data.total) {
        return httpResponse(200, { message: "No data to pull" });
      }
      processList = result.data.data.map((item) => {
        return item.vehicleNo;
      });
    }
    // processList = ["XE2864A", "XE3243K", "XE1227X"]; 
    console.log('LOG-processList', processList);
    console.log('LOG-length', processList.length);
    const startTime = Date.now();
    for (const [index, item] of processList.entries()) {
      console.log("LOG-index", index);
      await process({
        item,
        startOfYesterdaySingapore,
        endOfYesterdaySingapore,
        resultPullAscend,
      });

      if (Date.now() - startTime > 10 * 60 * 1000) {
        processList = processList.slice(index + 1);
        console.log("LOG-push sns");
        await snsClient.send(
          new PublishCommand({
            TopicArn: SNS_TOPIC_ARN,
            Message: JSON.stringify({
              handler: "pull_ascend_data",
              data: {
                startOfYesterdaySingapore,
                endOfYesterdaySingapore,
                resultPullAscend,
                processList,
              },
            }),
          })
        );
        break;
      }
    }
    console.log("LOG-resultPullAscend", resultPullAscend);

    const endTime = Date.now();
    await db.ascend().put({ id: "NOT_FOUND_VEHICLES", data: JSON.stringify(resultPullAscend.fails) });
    await db.session().put({
      id: `pull_data ${startTime} - ${endTime}`,
      session_id: `pull_data ${startTime} - ${endTime}`,
      jwt: `pull_data ${startTime} - ${endTime}`,
    });
    return httpResponse(200, { startOfYesterdaySingapore, endOfYesterdaySingapore, success: resultPullAscend.successes.length, fail: resultPullAscend.fails.length });
  } catch (error) {
    console.error("PullAscendDataHandler", error);
    return httpResponse(500, { error: error.message });
  }
};

const process = async ({
  item, // item === vehicleNo
  startOfYesterdaySingapore,
  endOfYesterdaySingapore,
  resultPullAscend,
}) => {
  try {
    const ascendResult = await ascend.getHistoricalData({
      truck_number: item,
      start: startOfYesterdaySingapore,
      end: endOfYesterdaySingapore,
    });
    const dataToStore = ascendResult.data;
    let jsonData = JSON.stringify(dataToStore);
    let compressedData = zlib.gzipSync(jsonData);
    const myDate = formatDateWithTimezone(
      new Date(startOfYesterdaySingapore),
      "Asia/Singapore",
      "YYYY-MM-DD"
    );
    const id = `${myDate}_${item}`;
    await db.ascend().put({ id, truck_number: item, date: myDate,  data: compressedData.toString("base64") });


    resultPullAscend.successes.push(item);
  } catch (error) {
    resultPullAscend.fails.push(item);
    console.error(`Error ${error.message} - vehicleNo ${item}`);
  }
  return resultPullAscend;
};


// Function to retrieve and decompress data
async function retrieveAndDecompressData(id: string) {
  try {
    const result = await db.ascend().get({ id });
    // const compressedData = Buffer.from(result.data, "base64"); // Convert base64 string back to buffer
    // const jsonData = zlib.gunzipSync(compressedData).toString("utf8"); // Decompress and convert to string
    // const data = JSON.parse(jsonData); // Parse JSON string to object
    // return data;
  } catch (error) {
    console.error(`Error retrieving data for id ${id}:`, error);
    throw error;
  }
}

export default handler;
