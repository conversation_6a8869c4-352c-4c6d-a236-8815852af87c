import {
  configService,
  formatDateWithTimezone,
  getMoment,
  getToken,
  httpResponse,
  randomString,
  replaceSpecialCharacters,
  request,
} from "@helpers";
import ascend from "@services/ascend.service";
import { DynamoDBService } from "@services/dynamodb.service";
import * as myExcel from "@helpers/excel";
import * as fs from "fs";
import { S3Service } from "@src/src/services/s3.service";
import AdmZip from "adm-zip";
import _ from "lodash";
import { CTRService } from "../services/ctr.service";
import { RoadNameService } from "../services/roadname.service";
import { SNSClient, PublishCommand } from "@aws-sdk/client-sns";
import zlib from "zlib";
import moment from "moment";

const db = new DynamoDBService();
const s3Service = new S3Service();
const ctrS3Service = new S3Service("CTR");
const roadNameService = new RoadNameService();
const snsClient = new SNSClient({ region: "ap-southeast-1" });
const SNS_TOPIC_ARN = configService.get("SNS_TOPIC_ARN");

const ONE_DAY_IN_MS = 24 * 60 * 60 * 1000;
const SEVEN_DAYS_IN_MS = 7 * ONE_DAY_IN_MS;
const FUNCTION_LIMIT_TIME = 5 * 60 * 1000;

interface TaskState {
  records: any[];
  filename: string;
  historyRequestPayload: any;
  truckIndex: number;
  processedCount: number;
  typeLocation: number;
  sheetsData: any[];
  exportData: any[];
}

type TransformedData = {
  VehicleNumber: string;
  "Date/Time": string;
  Latitude: string;
  Longitude: string;
  Roadname: string;
  Speed: number;
  "Data Source": string;
};

const validateParams = (params) => {
  const { truck_numbers, start, end } = params;
  if (!truck_numbers) {
    throw new Error("Missing required parameter: truck_numbers");
  }
  const truckNumbers = truck_numbers.split(",");
  if (truckNumbers.length !== new Set(truckNumbers).size) {
    throw new Error("Duplicated truck_numbers");
  }

  if (!start) {
    throw new Error("Missing required parameter: start");
  }
  if (!end) {
    throw new Error("Missing required parameter: end");
  }
};

const getAscendResponse = async ({ truck_number, start, end, reportId }) => {
  try {
    const diffInMs = end.getTime() - start.getTime();
    const chunks = Math.ceil(diffInMs / SEVEN_DAYS_IN_MS);
    const data = [];
    for (let i = 0; i < chunks; i++) {
      const chunkStart = new Date(start.getTime() + i * SEVEN_DAYS_IN_MS);
      const chunkEnd = new Date(
        Math.min(chunkStart.getTime() + SEVEN_DAYS_IN_MS, end.getTime())
      );
      const ascendResponse = await ascend.getHistoricalData({
        truck_number,
        start: chunkStart,
        end: chunkEnd,
      });
      data.push(...ascendResponse.data);
    }
    // handle chunk the time range into 7 days for each request
    return data;
  } catch (error) {
    console.log(reportId + "LOG-AscendError ", error);
    return [];
  }
};

const uploadJourneyToS3 = async ({ sheetsData, filename }) => {
  console.log("LOG-uploadJourneyToS3");
  const file = await myExcel.writeXLSX(filename, sheetsData);

  const uploadResult = await s3Service.uploadFile({
    key: filename,
    fileBuffer: file,
  });

  if (uploadResult) {
    const url = await s3Service.getUrl(
      filename,
      configService.getNumber("EXPIRE_SECONDS") || 60
    );
    return [url, filename];
  } else {
    throw new Error("Failed to upload file to S3");
  }
};

const transformFormatData = (data: any[]): TransformedData[] => {
  const transformedData: TransformedData[] = [];
  const uniqueEntries: Set<string> = new Set();
  const uniqueTimeEntries: Set<string> = new Set();

  data.forEach((entry, index) => {
    let vehicleNumber = "";
    let latitude = "0";
    let longitude = "0";
    let roadname = "";

    const nextEntry = data[index + 1];

    if ("uid" in entry) {
      // Ascend Data
      vehicleNumber = entry.name;
      // roadname = entry.road_name; // case data pulled in DB
      // roadname = entry.loc; // case data pulled in v2
      roadname = entry.roadName; // case data repaired
      if (entry.roadName === "NOT_FOUND") roadname = "";
      latitude = (+entry.lat).toFixed(4);
      longitude = (+entry.lng).toFixed(4);
    } else {
      // S3 Data
      const location = JSON.parse(entry.location);
      vehicleNumber = entry.vehicleno;
      latitude = (+location.coordinates[1]).toFixed(4);
      longitude = (+location.coordinates[0]).toFixed(4);
    }

    let nextEntryVehicleNumber;
    if (nextEntry) {
      if ("uid" in nextEntry) {
        nextEntryVehicleNumber = {
          vehicleNumber: nextEntry.name,
          latitude: nextEntry.lat,
          longitude: nextEntry.lng,
        };
      } else {
        const location = JSON.parse(nextEntry.location);
        nextEntryVehicleNumber = {
          vehicleNumber: nextEntry.vehicleno,
          latitude: location.coordinates[1],
          longitude: location.coordinates[0],
        };
      }
    }

    const nextVN = nextEntryVehicleNumber?.vehicleNumber;
    const nextLat = nextEntryVehicleNumber?.latitude;
    const nextLng = nextEntryVehicleNumber?.longitude;
    const nextKey = nextEntryVehicleNumber
      ? `${nextVN}-${nextLat}-${nextLng}`
      : null;

    const recordedAt = entry.dateTime || entry.recordedat;
    const timeRecord = formatDateWithTimezone(
      recordedAt,
      "Asia/Singapore",
      "DD/MM/YYYY hh:mm"
    );
    const key = `${vehicleNumber}-${latitude}-${longitude}`;

    if (
      (!uniqueEntries.has(key) || nextKey !== key) &&
      !uniqueTimeEntries.has(timeRecord)
    ) {
      uniqueTimeEntries.add(timeRecord);
      uniqueEntries.add(key);
      const formatted = formatDateWithTimezone(
        new Date(recordedAt),
        "Asia/Singapore",
        "DD/MM/YYYY hh:mm A"
      );
      const transformedEntry: any = {
        A: formatted,
        B: latitude,
        C: longitude,
        D: roadname, // Placeholder as road name extraction is not included
        E: entry.speed,
        F: "uid" in entry ? "GPS" : "APP",
      };
      transformedData.push(transformedEntry);
    }
  });
  console.log("LOG-transformedData", transformedData);
  return transformedData;
};

const getTheRoadName = async (latitude: string, longitude: string) => {
  const maxAttempts = 0;

  const wait = (ms: number) =>
    new Promise((resolve) => setTimeout(resolve, ms));

  const retryGetRoadName = async (
    latitude: string,
    longitude: string,
    attempts = 0
  ): Promise<string> => {
    try {
      const roadName = await roadNameService.getRoadName({
        latitude,
        longitude,
      });
      if (roadName === "NOT_FOUND" && attempts < maxAttempts) {
        await wait(1000); // Wait for 1 second before retrying
        return retryGetRoadName(latitude, longitude, attempts + 1);
      }
      return roadName;
    } catch (error) {
      if (attempts < maxAttempts) {
        await wait(1000); // Wait for 1 second before retrying
        return retryGetRoadName(latitude, longitude, attempts + 1);
      }
      return "NOT_FOUND";
    }
  };

  return retryGetRoadName(latitude, longitude);
};

function chunkArray(array, size) {
  const chunkedArr = [];
  for (let i = 0; i < array.length; i += size) {
    chunkedArr.push(array.slice(i, i + size));
  }
  return chunkedArr;
}

async function handlerLimitRequest(promise, promises, result = []) {
  try {
    const data = await promise();
    result.push(data);
  } catch (error) {
    console.log("handlerLimitRequest :", error);
    // promises.push(promise);
  } finally {
    const nextPromise = promises.shift();
    if (nextPromise) {
      await handlerLimitRequest(nextPromise, promises, result);
    }
  }
}

async function limitRequest(promises, limit = 10) {
  const result = [];
  while (promises.length > 0) {
    const workers = promises.splice(0, limit);
    await Promise.all(
      workers.map((pm) => handlerLimitRequest(pm, promises, result))
    );
    // await new Promise((resolve) => setTimeout(resolve, 1000));
  }
  return result;
}

const loadLocationData = async ({
  locationData,
  trucksToLoad,
  start,
  end,
  filename,
  reportId,
  historyRequestPayload,
  indexDate = 0,
}) => {
  console.log("LOG-indexDate", indexDate);
  const startProcessTime = Date.now();
  const startDate = new Date(start);
  const endDate = new Date(end);

  const s3Files = await ctrS3Service.listFilesInFolder("vehicletrackings");
  let isOverTime = false;
  // load APP
  for (
    let d = new Date(startDate), index = 0;
    d < new Date(endDate);
    d.setDate(d.getDate() + 1), index++
  ) {
    if (index < indexDate) continue;
    indexDate = index;

    const unzippedPath = `/tmp/unzipped/${d.getTime()}/`;

    // loop day by day from start to end date and filter the file includes the date
    if (!fs.existsSync(unzippedPath)) {
      fs.mkdirSync(unzippedPath, { recursive: true });
    }

    const date = formatDateWithTimezone(d, "Asia/Singapore").split("T")[0];
    const s3FileKeys = s3Files.filter((f: string) => f.includes(date));
    let files = [];
    let oneDayData = [];

    try {
      async function loadFromS3File() {
        await Promise.all(
          s3FileKeys.map(async (s3FileKey) => {
            // console.log("LOG-fs.existsSync(downloadPath)", fs.existsSync(downloadPath));
            const downloadPath = `/tmp/${s3FileKey.replace(
              "vehicletrackings/",
              ""
            )}`;

            if (!fs.existsSync(downloadPath)) {
              await ctrS3Service.downloadFileFromS3(s3FileKey, downloadPath);
            }
            if (fs.existsSync(downloadPath)) {
              const zip = new AdmZip(downloadPath);
              zip.extractAllTo(unzippedPath, true);
            } else {
              return [s3FileKey, "Failed to download file"];
            }
            files = fs.readdirSync(unzippedPath);
          })
        );

        console.log("LOG-files", files);
        console.log("LOG-files.length", files.length);

        let oneDayData = [];
        files.map((file: string) => {
          if (file.includes("vehicle-tracks")) {
            // we have only 1 file
            const data = myExcel.readXLSX(`${unzippedPath}${file}`);
            if (data.length && data[0]) {
              // console.log("LOG-locations in " + data[0].length, file);
              oneDayData = data[0];
            }
          }
        });
        return oneDayData;
      }

      async function loadFromDB() {
        const oneDayData = [];
        const date = formatDateWithTimezone(d, "Asia/Singapore", "YYYY-MM-DD");
        console.log("LOG-date", date);
        const responseDB = await db.repairing().get({
          date,
          type: "app",
        });
        console.log("LOG-responseDB", responseDB);
        responseDB.items.map((item) => {
          const compressedData = Buffer.from(item.data, "base64");
          const jsonData = zlib.gunzipSync(compressedData).toString();
          const response = JSON.parse(jsonData);
          oneDayData.push(...response);
        });

        return oneDayData;
      }

      oneDayData = await loadFromDB();
      if (!oneDayData.length) oneDayData = await loadFromS3File();

      console.log("LOG-oneDayData.length", oneDayData);
      trucksToLoad.map((truck_number: string) => {
        const truckData = locationData[`${truck_number}`];
        if (!truckData) {
          locationData[`${truck_number}`] = {
            GPS: [],
            APP: [],
          };
        }
        const s3Data = oneDayData.filter(
          (truck) => truck.vehicleno === truck_number
        );
        locationData[`${truck_number}`]["APP"].push(
          ...transformFormatData(s3Data)
        );
        console.log(
          "LOG-APP-total: " + truck_number,
          locationData[`${truck_number}`]["APP"].length
        );
      });
    } catch (error) {
      console.log("LOG-error 1", error);
      console.log("LOG-HANDLE_END");
      await db.truckJourneyHistory().put({
        ...historyRequestPayload,
        completed_at: new Date().toISOString(),
        error: error.response ? error.response.data?.msg : error.message,
      });

      throw error;
    } finally {
      // remove all file downloaded and extracted
      fs.rmdirSync(unzippedPath, { recursive: true });
      const tmpDir = "/tmp/";
      const files = fs.readdirSync(tmpDir);
      console.log("LOG-remove-files", files);

      // Iterate over each item and remove it
      files.forEach((file) => {
        const filePath = tmpDir + file;
        const stat = fs.lstatSync(filePath);

        if (stat.isDirectory()) {
          // Remove directory and its contents
          fs.rmdirSync(filePath, { recursive: true });
        } else {
          // Remove file
          fs.unlinkSync(filePath);
        }
      });
    }

    if (Date.now() - startProcessTime > FUNCTION_LIMIT_TIME) {
      console.log("PUSH SNS from load location");
      await s3Service.uploadFile({
        key: `${reportId}.json`,
        fileBuffer: JSON.stringify({
          records: locationData,
          trucksToLoad,
          start,
          end,
          filename,
          reportId,
          historyRequestPayload,
          indexDate: indexDate + 1,
        }),
      });

      await snsClient.send(
        new PublishCommand({
          TopicArn: SNS_TOPIC_ARN,
          Message: JSON.stringify({
            handler: "truck_journey_history",
            url: `${reportId}.json`,
            isLoadingLocationData: true,
            report_id: reportId,
          }),
        })
      );
      isOverTime = true;
      break;
    }
  }

  if (isOverTime) {
    return [locationData, isOverTime];
  }

  // load Ascend
  for (
    let d = new Date(startDate);
    d < new Date(endDate);
    d.setDate(d.getDate() + 1)
  ) {
    await Promise.all(
      trucksToLoad.map(async (truck_number: string) => {
        const truckData = locationData[`${truck_number}`];
        if (!truckData) {
          locationData[`${truck_number}`] = {
            GPS: [],
            APP: [],
          };
        }
        // read from db
        const date = formatDateWithTimezone(d, "Asia/Singapore", "YYYY-MM-DD");
        console.log("LOG-date", date);
        const responseDB = await db.repairing().get({
          date,
          type: "gps",
          truck_number,
        });
        console.log("LOG-responseDB GPS", responseDB);
        responseDB.items.map((item) => {
          const compressedData = Buffer.from(item.data, "base64");
          const jsonData = zlib.gunzipSync(compressedData).toString();
          const response = JSON.parse(jsonData);
          locationData[`${truck_number}`]["GPS"].push(
            ...transformFormatData(response)
          );
        });

        console.log("LOG-!responseDB.items.length", !responseDB.items.length);
        if (!responseDB.items.length) {
          console.log(`1`, getMoment(d).startOf("day").valueOf());
          const as = await getAscendResponse({
            truck_number,
            start: new Date(getMoment(d).startOf("day").valueOf()),
            end: new Date(getMoment(d).endOf("day").valueOf()),
            reportId,
          });
          console.log("LOG-as", as);
          locationData[`${truck_number}`]["GPS"].push(
            ...transformFormatData(as)
          );
        }
      })
    );
  }

  // --
  console.log("LOG-end_loop");
  // log each key of locationData and the length of the data
  Object.keys(locationData).forEach((key) => {
    console.log("LOG-" + key + " GPS: " + locationData[key].GPS.length);
    console.log("LOG-" + key + " APP: " + locationData[key].APP.length);
  });
  return [locationData, false];
};

const checkRecordToBreak = async (report_id) => {
  const isExist = await db.truckJourneyHistory().get({ report_id });
  if (!isExist.items.length) {
    console.error(
      "LOG-" + report_id + " Process is break by report is removed in DB"
    );
    throw new Error("Process is break by report is removed in DB");
  }
};

const handler = async (event, context) => {
  let taskState: TaskState;
  const ctrService = new CTRService();
  let reportId;
  if ("Records" in event && event.Records[0].Sns) {
    console.log("LOG-sns");
    // This is an SNS event
    const messagePushed = JSON.parse(event.Records[0].Sns.Message);
    const url = messagePushed.url;
    reportId = messagePushed.report_id;

    await checkRecordToBreak(reportId);
    console.log("LOG-event.Records[0].Sns", event.Records[0].Sns);
    const randomName = randomString(3, { character: true });
    await s3Service.downloadFileFromS3(url, `/tmp/${randomName}`);
    const fileContent = fs.readFileSync(`/tmp/${randomName}`, "utf8");

    if (messagePushed.isLoadingLocationData) {
      const payload = JSON.parse(fileContent);
      const {
        records,
        filename,
        trucksToLoad,
        start,
        end,
        reportId,
        historyRequestPayload,
        indexDate,
      } = payload;

      const [locationData, isOverTime]: any = await loadLocationData({
        locationData: records,
        filename,
        trucksToLoad,
        start: new Date(start),
        end: new Date(end),
        reportId,
        historyRequestPayload,
        indexDate,
      });

      if (isOverTime) {
        return;
      }

      taskState = {
        records: locationData,
        filename,
        historyRequestPayload,
        processedCount: 0,
        truckIndex: 0,
        typeLocation: 0,
        sheetsData: [],
        exportData: [],
      };
    } else {
      taskState = JSON.parse(fileContent);
    }
    // remove the file after reading
    console.log("LOG-taskState", taskState);
  } else {
    // This is an API Gateway event, initialize the task

    const queryParams = event.queryStringParameters;
    let { company } = queryParams;
    if (context.user.company) company = context.user.company;
    if (!company) company = "CDAS";

    validateParams(queryParams);
    const { truck_numbers, start, end, sendToMail } = queryParams;
    const truckNumbers = truck_numbers.split(",");
    const isValidTruck = await ctrService.isValidTruck(
      truckNumbers,
      company,
      await getToken()
    );
    if (!isValidTruck)
      throw new Error("Invalid truck_numbers, or not belong to the company");

    const formattedStart = formatDateWithTimezone(
      new Date(+start * 1000),
      "Asia/Singapore",
      "DDMMYYYY_hhmmA"
    );

    const formattedEnd = formatDateWithTimezone(
      new Date(+end * 1000),
      "Asia/Singapore",
      "DDMMYYYY_hhmmA"
    );
    reportId = randomString(8);
    let companyName = "NOT_FOUND";
    const result = await request({
      method: "GET",
      url: `${configService.get("CTR_URL")}/vehicles?vehicleNo[$in][]=${
        truckNumbers[0]
      }`,
      token: await getToken(),
    });
    if (result.data.total) {
      const companyId = result.data.data[0]?.company;
      const companyResponse = await request({
        method: "GET",
        url: `${configService.get("CTR_URL")}/company`,
        token: await getToken(),
      });
      companyResponse.data.forEach((company) => {
        if (company._id === companyId) {
          companyName = company.name;
        }
      });
    }

    const filename = `${reportId}_${replaceSpecialCharacters(
      companyName
    )}_${formattedStart}_${formattedEnd}.xlsx`;

    const id = `${truck_numbers}-${start}-${end}`;
    const existingRequest = await db.truckJourneyHistory().get({ id });
    if (existingRequest.items.length > 0) {
      const file = existingRequest.items[0].filename;
      if (file) {
        return httpResponse(200, {
          url: await s3Service.getUrl(
            file,
            configService.getNumber("EXPIRE_SECONDS") || 60
          ),
        });
      }

      if (!existingRequest.items[0].error) {
        throw new Error("Request is processing, please wait for the result");
      }
    }

    const created_at = new Date().toISOString();
    const historyRequestPayload = {
      id: `${truck_numbers}-${start}-${end}`,
      truck_number: truck_numbers,
      sendToMail: sendToMail || "",
      report_id: reportId,
      start: +start,
      end: +end,
      company,
      created_at,
      created_by: _.pick(context.user, [
        "_id",
        "email",
        "username",
        "fullname",
      ]),
      error: "",
    };

    await db.truckJourneyHistory().put(historyRequestPayload);

    const [locationData, isOverTime]: any = await loadLocationData({
      locationData: {},
      filename,
      trucksToLoad: truckNumbers,
      start: new Date(+start * 1000),
      end: new Date(+end * 1000),
      reportId,
      historyRequestPayload,
    });

    if (isOverTime) {
      return;
    }
    taskState = {
      records: locationData,
      filename,
      historyRequestPayload,
      processedCount: 0,
      truckIndex: 0,
      typeLocation: 0,
      sheetsData: [],
      exportData: [],
    };
  }

  // records = { TR0987: { GPS: [], APP: [] } }
  const startTime = Date.now();
  while (true) {
    let {
      truckIndex,
      processedCount,
      typeLocation,
      filename,
      historyRequestPayload,
      sheetsData,
    } = taskState;
    try {
      console.log("LOG-filename", filename);
      const keyTruck = Object.keys(taskState.records)[truckIndex];
      console.log("LOG-truckIndex", truckIndex);
      console.log("LOG-keyTruck", keyTruck);

      if (!keyTruck) {
        console.log("LOG-upload");
        const [url] = await uploadJourneyToS3({
          sheetsData,
          filename,
        });

        await db.truckJourneyHistory().put({
          ...historyRequestPayload,
          completed_at: new Date().toISOString(),
          filename,
        });

        const { sendToMail } = historyRequestPayload;
        console.log("LOG-sendToMail", sendToMail);
        const { fullname } = historyRequestPayload.created_by;
        const receiver = sendToMail
          ? {
              fullname,
              email: sendToMail,
            }
          : historyRequestPayload.created_by;
        await ctrService.sendReportCompleted({
          receiver,
          link: url,
        });

        return httpResponse(200, {
          url,
        });
      }
      const truck = taskState.records[keyTruck]; // { GPS: [], APP: [] }
      while (true) {
        function getRoadname(element) {
          return new Promise(async (resolve) => {
            const lat = element.B;
            const lng = element.C;
            const existRoadname = element.D;
            if (lat && lng && !existRoadname) {
              // element.Roadname = await getTheRoadName(
              const roadname = await getTheRoadName(
                lat.toString(),
                lng.toString()
              );
              element["D"] =
                roadname == "NOT_FOUND" || roadname == "NIL" ? "" : roadname;
            }
            resolve(element);
          });
        }
        const keyLocation = Object.keys(truck)[typeLocation]; // GPS, APP
        if (!keyLocation) {
          taskState.typeLocation = 0;
          break;
        }
        const data = truck[keyLocation]; // data = [ { VehicleNumber: string, RecordDateTime: string, Latitude: number, Longitude: number, Roadname: string, Speed: number, TypeOfData: string } ]
        const chunks = chunkArray(data, 200); // [[], [], []]

        console.log("LOG-chunks.length", chunks.length);
        let exportData = taskState.exportData ? taskState.exportData : [];
        for (let i = processedCount; i < chunks.length; i++) {
          const chunk = chunks[i];
          const promises = chunk.map((element) =>
            getRoadname.bind(null, element)
          );

          exportData.push(await limitRequest(promises, 100));
          console.log(
            "LOG-Export ",
            `${keyTruck} [${keyLocation}]: ${exportData.length} / ${chunks.length}`
          );
          taskState.processedCount = i;

          if (Date.now() - startTime > FUNCTION_LIMIT_TIME) {
            taskState.exportData = exportData;
            await s3Service.uploadFile({
              key: filename.replace(".xlsx", ".json"),
              fileBuffer: JSON.stringify(taskState),
            });

            console.log("PUSH SNS from read name");
            await snsClient.send(
              new PublishCommand({
                TopicArn: SNS_TOPIC_ARN,
                Message: JSON.stringify({
                  handler: "truck_journey_history",
                  url: filename.replace(".xlsx", ".json"),
                  isLoadingLocationData: false,
                  report_id: reportId,
                }),
              })
            );

            return;
          }
        }
        if (keyLocation === "APP") {
        } else {
          exportData = data;
        }

        const response = await request({
          method: "GET",
          url: `${configService.get(
            "CTR_URL"
          )}/vehicles?vehicleNo%5B%24regex%5D=${keyTruck}&vehicleNo%5B%24options%5D=i`,
          token: await getToken(),
        });

        let companyName = "";
        if (response.data?.total) {
          const companyId = response.data.data[0]?.company;
          const companyResponse = await request({
            method: "GET",
            url: `${configService.get("CTR_URL")}/company`,
            token: await getToken(),
          });
          companyResponse.data.forEach((company) => {
            if (company._id === companyId) {
              companyName = company.name;
            }
          });
        }

        exportData = exportData.flat();

        exportData.sort((a, b) => {
          const dateA = moment(a.A, "DD/MM/YYYY hh:mm A");
          const dateB = moment(b.A, "DD/MM/YYYY hh:mm A");
          return dateA.valueOf() - dateB.valueOf();
        });

        sheetsData.push({
          name: `${keyLocation} ${keyTruck}`,
          data: [
            {
              A: "Company Name",
              B: companyName,
            },
            {
              A: "Vehicle Number",
              B: keyTruck,
            },
            {
              A: "Start Date & Time",
              B: formatDateWithTimezone(
                new Date(historyRequestPayload.start * 1000),
                "Asia/Singapore",
                "DD/MM/YYYY hh:mm:ss A"
              ),
            },
            {
              A: "End Date & Time",
              B: formatDateWithTimezone(
                new Date(historyRequestPayload.end * 1000),
                "Asia/Singapore",
                "DD/MM/YYYY hh:mm:ss A"
              ),
            },
            {},
            {
              A: "Date & Time",
              B: "Latitude",
              C: "Longitude",
              D: "Street",
              E: "Speed (km/h)",
              F: "Data Source",
            },
            ...exportData,
          ],
        });

        taskState.exportData = [];
        typeLocation++;
        processedCount = 0;
      }

      taskState.typeLocation = 0;
      taskState.processedCount = 0;
      taskState.truckIndex++;
    } catch (error) {
      console.log("LOG-error 2", error);
      console.log("LOG-HANDLE_END");
      await db.truckJourneyHistory().put({
        ...historyRequestPayload,
        completed_at: new Date().toISOString(),
        error: error.response ? error.response.data?.msg : error.message,
      });
      throw error;
    }
  }
};

export default handler;
