import { httpResponse } from "../helpers";
import { DynamoDBService } from "../services/dynamodb.service";
import { LicensingValidator } from "../validators/licensing.validator";
import _ from "lodash";

const db = new DynamoDBService();

type TLicense = {
  company_id: string;
  status: number;
  users: Array<any>;
  users_in_use: number;
  users_allowed: number;
  end_date: string;
  start_date: string;
  permissions: Array<{
	code: string; // journey_history, message_history, etc
	name: string;
	status: number;
  }>;
  history: Array<TLicense>
};

const createHandler = async (event, context) => { 
	try {
		const payload: TLicense = JSON.parse(event.body);
		const license = new LicensingValidator(payload);
		const errorMessage = await license.getErrorMessage();
		console.log('LOG-errorMessage', errorMessage);
		if (errorMessage) return httpResponse(400, { error: errorMessage });

		const { company_id } = payload;
		const data = await db.licensing().get({ company_id });
		if (data.items.length > 0) return httpResponse(400, { error: "Company already has a license" });
		payload.status = 1;
		payload.users_in_use = payload.users.filter(user => user.status === true).length;
		payload.history = [];

		await db.licensing().put(payload);
		return httpResponse(200, { licensing: payload });
	} catch (error) {
		return httpResponse(500, { error: error.message });
	}
}

const getHandler = async (event, context) => { 
	try {
		// check case get detail or get all
		let data;
		if (!context.isCDAS) event.queryStringParameters = { company_id: context.user.company };
		if (event.queryStringParameters) {
			data = await db.licensing().get(event.queryStringParameters);
			if (data.items.length === 0) return httpResponse(404, { error: "Not found" });
        	return httpResponse(200, { license: data.items[0] });
		} else {
			data = await db.licensing().getAll();

			const transformed = {};

			data.items.forEach((license) => {
				license.permissions.forEach((permission) => {
					if (!transformed[permission.code]) {
						transformed[permission.code] = [];
					}
					if (license.status && permission.status === 1) {
						// Only include permissions with status 1
						transformed[permission.code].push(license);
					}
				});
			});

			return httpResponse(200, { licenses: data.items, services: transformed });
		}
	} catch (error) {
		return httpResponse(500, { error: error.message });
	}
}

const updateHandler = async (event, context) => {
	try {
		const payload = JSON.parse(event.body);
		const licenseValidator = new LicensingValidator(payload);
		const errorMessage = await licenseValidator.getErrorMessage();
		if (errorMessage) return httpResponse(400, { error: errorMessage });

		const { company_id } = payload;
		const data = await db.licensing().get({ company_id });
		if (data?.items?.length === 0) return httpResponse(404, { error: "Not found" });

		const license = data.items[0];
		// get the difference fields between the old and new license
		const diff = Object.keys(payload).filter((key) => {
			if (!_.isEqual(payload[key], license[key])) {
				return {
					field: key,
					oldValue: license[key],
					newValue: payload[key]
				};
			}
		}).map((key) => ({
			field: key,
			oldValue: license[key],
			newValue: payload[key]
		}));
		payload.history = license.history || [];
		payload.history.push({updated_at: new Date().toISOString() , changes: diff});
		payload.users_in_use = payload.users.filter((user) => user.status === true).length;

		await db.licensing().put(payload);
		return httpResponse(200, { licensing: payload });
	} catch (error) {
		return httpResponse(500, { error: error.message });
	}
}

export default {
  createHandler,
  getHandler,
  updateHandler,
};