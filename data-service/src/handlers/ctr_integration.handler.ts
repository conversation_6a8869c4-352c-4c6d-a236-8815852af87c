import { configService, httpResponse, generateCode } from "@helpers";
import { DynamoDBService } from "@services/dynamodb.service";


const db = new DynamoDBService();

export const linkDataServiceHandler = async (event, context) => {
  const session_id = generateCode(10);
  await db.session().put({
    id: session_id,
    session_id,
    jwt: context.user.jwt,
    user_id: context.user._id,
  });

  return {
    statusCode: 200,
    body: JSON.stringify({
      redirect_url: `${configService.get(
        "DATA_SERVICE_URL"
      )}?session_id=${session_id}`,
    }),
    headers: {
      "Access-Control-Allow-Origin": "*", // for CORS
      "Access-Control-Allow-Credentials": true, // for cookies, authorization headers, etc.
      "Access-Control-Allow-Headers": "Content-Type", // for POST, PUT requests
    },
  };
  
};

export const getAuthTokenHandler = async (event, context) => {
  const { session_id } = event.queryStringParameters;
  if (!session_id) return httpResponse(400, { error: "Session ID is required" });
  const session = await db.session().get({ session_id });

  if (!session.items.length) {
    return httpResponse(404, {session, error: "Session not found" });
  }

  return httpResponse(200, { jwt: session.items[0].jwt });
};

export const validateTokenHandler = async (event, context) => {
  const user = context.user;
  return httpResponse(200, { user });
};
