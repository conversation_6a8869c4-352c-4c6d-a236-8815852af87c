import {
  configService,
  formatDateWithTimezone,
  httpResponse
} from "../helpers";
import { DynamoDBService } from "../services/dynamodb.service";
import moment_tz from "moment-timezone";
import zlib from "zlib";
import { RoadNameService } from "../services/roadname.service";
import { SNSClient, PublishCommand } from "@aws-sdk/client-sns";


const snsClient = new SNSClient({ region: "ap-southeast-1" });
const SNS_TOPIC_ARN = configService.get("SNS_TOPIC_ARN");
const roadNameService = new RoadNameService();
const db = new DynamoDBService();
const handler = async (event) => {

  let yesterdayItems = [];
  if ("Records" in event) { 
    const { data } = JSON.parse(event.Records[0].Sns.Message);
    yesterdayItems = data.yesterdayItems
  } else {
    let startOfYesterdaySingapore = moment_tz()
      .tz("Asia/Singapore")
      .subtract(1, "day")
      .startOf("day")
      .toDate()
      .getTime();
    const yesterday = formatDateWithTimezone(
      new Date(startOfYesterdaySingapore),
      "Asia/Singapore",
      "YYYY-MM-DD"
    );
    const yesterdayData = await db.ascend().get({ date: yesterday });
    yesterdayItems = yesterdayData.items;
  }
  const startTime = Date.now();
  console.log('LOG-yesterdayItems', yesterdayItems[0]);

  for (const [index, item] of yesterdayItems.entries()) {
    const compressedData = Buffer.from(item.data, "base64");
    const jsonData = zlib.gunzipSync(compressedData).toString();
    const locations = JSON.parse(jsonData);

    console.log("LOG-locations.length", locations.length);
    const promises = locations.map((element) =>
      getRoadname.bind(null, element)
    );
    const result = await limitRequest(promises, 10);
    console.log('LOG-result', result);
    let newCompressedData = zlib.gzipSync(JSON.stringify(result));
    const { id, date, truck_number } = item;
    await db.ascend().put({
      id,
      truck_number,
      date,
      data: newCompressedData.toString("base64"),
    });
    if (Date.now() - startTime > 10 * 60 * 1000) {
      yesterdayItems = yesterdayItems.slice(index + 1);
      await snsClient.send(
        new PublishCommand({
          TopicArn: SNS_TOPIC_ARN,
          Message: JSON.stringify({
            handler: "cronjob_roadname",
            data: {
              yesterdayItems,
            },
          }),
        })
      );
      break;
    }
  }

  const endTime = Date.now();

  await db.session().put({
    id: `cronjob_roadname ${startTime} - ${endTime}`,
    session_id: `cronjob_roadname ${startTime} - ${endTime}`,
    jwt: `cronjob_roadname ${startTime} - ${endTime}`,
  });

  return httpResponse(200, {});
};

async function handlerLimitRequest(promise, promises, result = []) {
  try {
    const data = await promise();
    result.push(data);
  } catch (error) {
    console.log("handlerLimitRequest :", error);
    // promises.push(promise);
  } finally {
    const nextPromise = promises.shift();
    if (nextPromise) {
      // await handlerLimitRequest(nextPromise, promises, result);
    }
  }
}
async function limitRequest(promises, limit = 10) {
  const result = [];
  while (promises.length > 0) {
    console.log('LOG-promises.length', promises.length);
    const workers = promises.splice(0, limit);
    await Promise.all(
      workers.map((pm) => handlerLimitRequest(pm, promises, result))
    );
    await new Promise((resolve) => setTimeout(resolve, 500));
  }
  return result;
}
async function getRoadname(element) {
  return new Promise(async (resolve) => {
    const lat = element.lat;
    const lng = element.lng;
    try {
      if (lat && lng) {
        // element.Roadname = await getTheRoadName(
        const roadname = await roadNameService.getRoadName({
          latitude: lat.toString(),
          longitude: lng.toString(),
        });
        element["road_name"] =
          roadname == "NOT_FOUND" || roadname == "NIL" ? "" : roadname;
      }
    } catch (error) {
      console.log("LOG-getRoadname", error);
    }
    resolve(element);
  });
}

export default handler;