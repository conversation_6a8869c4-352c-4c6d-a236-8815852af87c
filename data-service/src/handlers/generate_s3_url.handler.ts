import { S3Service } from "@services/s3.service";
import { configService, httpResponse } from "../helpers";
import { CTRService } from "../services/ctr.service";

const handler = async (event, context) => {
  try {
    const s3Service = new S3Service();
    const ctrService = new CTRService();
    const queryParams = event.queryStringParameters;
    const { filename } = queryParams;

    if (!filename)
      throw new Error("The file does not exist. Please create a new request.");
    const url = await s3Service.getUrl(
      filename,
      configService.getNumber("EXPIRE_SECONDS") || 60
    );
    return httpResponse(200, { url });
  } catch (error) {
    console.error("generateS3UrlHandler", error);
    return httpResponse(500, { error: error.message });
  }
};
export default handler;
