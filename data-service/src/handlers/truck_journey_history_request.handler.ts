import { httpResponse } from "@helpers";
import { DynamoDBService } from "@services/dynamodb.service";

const db = new DynamoDBService();

const handler = async (event, context) => {
      let { company } = event.queryStringParameters;
      delete event.queryStringParameters.company;
      if (context.user.company) company = context.user.company;
      
      if (!company) return httpResponse(400, { error: "Company is required" });
      const st = await db.truckJourneyHistory().get({ company: company });
      return httpResponse(200, { history_requests: st, company });
}

export default handler;
