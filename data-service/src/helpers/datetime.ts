import moment_tz from 'moment-timezone';
import ConfigService from "@helpers/config";
moment_tz.locale('en-sg');

export function formatDateWithTimezone(
  date: string | Date,
  tz = ConfigService.getInstance().get('TIMEZONE') || 'Asia/Singapore',
  format = '',
) {
  return moment_tz(date || new Date())
    .tz(tz)
    .format(format);
}

export function formatDateWithTimezone2(
  date: string | Date,
  tz = ConfigService.getInstance().get('TIMEZONE') || 'Asia/Singapore',
  format = '',
) {
  return moment_tz(date || new Date())
    .tz(tz)
    .format(format);
}
export function getMoment(
  date: Date | string = new Date(),
  tz = ConfigService.getInstance().get('TIMEZONE') || 'Asia/Singapore',
) {
  return moment_tz(date).tz(tz);
}

export function parseDate(
  date,
  format,
  tz = ConfigService.getInstance().get('TIMEZONE') || 'Asia/Singapore',
) {
  return moment_tz.tz(date, format, tz);
}

export function parseExcelDate(days) {
  const date = new Date((days - 25569) * 86400 * 1000);
  return `${(date.getMonth() + 1).toString().padStart(2, '0')}/${date
    .getDate()
    .toString()
    .padStart(2, '0')}/${date.getFullYear()}`;
}
