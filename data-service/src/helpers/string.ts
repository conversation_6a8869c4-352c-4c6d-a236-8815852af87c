import { config } from 'aws-sdk';
import moment from 'moment-timezone';
import * as uuid from 'uuid';
import * as _ from 'lodash';
import { configService } from '@helpers';

declare global {
  interface String {
    singleChar: (char?: string) => string;
    toItemUid: () => string;
    capitalize: () => string;
  }
}

String.prototype.singleChar = function (char = ' ') {
  return this.replace(new RegExp(`${char}+`), char).trim();
};

String.prototype.toItemUid = function () {
  return this.replace(/\s/g, '_').toLowerCase();
};

String.prototype.capitalize = function () {
  return this.charAt(0).toUpperCase() + this.slice(1).toLowerCase();
};

export function generateQrcode(size = 16) {
  return generateCode(size, false).toLowerCase();
}

export function generateCode(n = 6, numberOnly = true) {
  let text = '';
  let charset = '';
  const possible =
    'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
  const numOnly = '0123456789';
  if (numberOnly) {
    charset = numOnly;
  } else {
    charset = possible;
  }
  for (let i = 0; i < n; i++) {
    text += charset.charAt(Math.floor(Math.random() * charset.length));
  }
  return text;
}

export function randomString(
  length = 8,
  {
    number = true,
    character = true,
    special = false,
    format = null,
    exclude = [],
    source = null,
  } = {},
) {
  const CHARACTERS = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz';
  const SPECIALS = '!@#$%^&*.+-/|~';
  const NUMBERS = '1234567890';
  const charset = _.difference(
    source?.split('') ||
      [number && NUMBERS, character && CHARACTERS, special && SPECIALS]
        .filter((s) => s)
        .join('')
        .split(''),
    exclude,
  );
  const randChars = _.sampleSize(charset, length).join('');
  return format ? format(randChars) : randChars;
}


export function formatDate(date, format = 'LLL', tz = 'Asia/Singapore') {
  return moment(date).tz(tz).format(format);
}

export function generateUUID() {
  return uuid.v4();
}

export function getWeek(date) {
  const year = moment(date).tz('Asia/Singapore').startOf('week').get('years');
  const week = moment(date).tz('Asia/Singapore').get('isoWeeks');
  return `${year}_${(week + '').padStart(2, '0')}`;
}

export function mapDataToString(pattern, data) {
  return pattern.replace(/\{\{(\w+)\}\}/g, (matched, captured) =>
    data[captured] !== undefined ? data[captured] : matched,
  );
}

export function maskMobileNumber(mobileNumber) {
  if (!mobileNumber) return '';
  return '**** ' + mobileNumber.slice(-4);
}

export function normalizeNotionString(
  str: string,
  // cascade
  charMap: [string | RegExp, string][] = [
    [/\n/, ''],
    [/\s+/, ' '],
  ],
) {
  if (!str) return '';
  let result = str;
  for (const [pattern, replacement] of charMap)
    result = result.replace(new RegExp(pattern, 'g'), replacement).trim();
  return result;
}

export function getUUID(): string {
  return generateUUID().split('-').join('');
}

export function replaceSpecialCharacters(str: string) {
  return str.replace(/[^a-zA-Z0-9]/g, '_');
}