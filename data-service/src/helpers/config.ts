import { config } from "dotenv";
import fs from "fs";
import path from "path";

export default class ConfigService {
  private static _instance: ConfigService;

  constructor() {
    const envFilePath = fs.existsSync(".env.development")
      ? ".env.development"
      : ".env";
    config({
      path: envFilePath,
    });
  }

  static getInstance() {
    if (this._instance) return this._instance;
    this._instance = new ConfigService();
    Object.freeze(this._instance);
    return this._instance;
  }

  get hmacSecret(): string {
    return this.get("HMAC_SECRET");
  }

  get isDevelopment(): boolean {
    return this.nodeEnv === "development";
  }

  get isProduction(): boolean {
    return this.nodeEnv === "production";
  }
  
  get tokenExpiresTime(): string {
    return this.get("TOKEN_EXPIRES_TIME") || "7d";
  }

  get nodeEnv(): string {
    return this.get("NODE_ENV") || "development";
  }

  public get(key: string): string {
    return process.env[key] || "";
  }

  public getNumber(key: string): number {
    return Number(this.get(key)) || 0;
  }
 
  public getDatabaseConfig() {
    return {
      region: this.get("AWS_REGION"),
      accessKeyId: this.get("DB_ACCESS_KEY_ID"),
      secretAccessKey: this.get("DB_SECRET_ACCESS_KEY"),
    };
  }
  
}

export const configService = ConfigService.getInstance();

