import axios from 'axios'
import { configService } from './config';


export const request = async (data: {
  method: string; // 'GET' | 'POST' | 'PUT' | 'DELETE'
  url: string;
  token?: string;
  body?: any;
}) => {
  /* ... */
  let options = {
    method: data.method,
    url: data.url,
    headers: {
      Authorization: data.token || null,
      "content-type": "application/json",
      "cache-control": "no-cache",
    },
    data: data.body,
  };
  return await axios(options);
};

let token, expire;
export const getToken =  async () => {
    if (token && new Date().getTime() < expire) {
      return token;
    };
    const data = {
      method: "POST",
      url: `${configService.get("CTR_URL")}/authentication`,
      body: {
        strategy: "local",
        email: configService.get("CDAS_EMAIL"),
        password: configService.get("CDAS_PASSWORD"),
        device: {
          os: "computer",
          FCMId: "portal",
        }
      },
    };
    const result = await request(data);
    token = result.data.accessToken;
    expire = result.data.authentication.payload.exp * 1000;
    return result.data.accessToken;
  }