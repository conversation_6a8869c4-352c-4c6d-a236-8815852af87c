import XLSX_POPULATE from 'xlsx-populate';
import csv from 'csv-parser';
import XLSX from 'xlsx';
import fs from 'fs';
import { getMoment } from '@helpers/datetime';
import * as ExcelJS from "exceljs";
import { configService } from './config';
import { S3Service } from "@src/src/services/s3.service";

export function readXLSX(filePath: string, options: any = {}) {
  const wb = XLSX.readFile(filePath, options);
  const result = [];
  for (const sheetName of wb.SheetNames) {
    result.push(
      XLSX.utils.sheet_to_json<object>(wb.Sheets[sheetName]).map((obj) => {
        const temp = {};
        Object.entries(obj).map(([key, value]) => {
          temp[key.singleChar().trim().toItemUid()] = value;
        });
        return temp;
      }),
    );
  }
  return result;
}

export async function readXLSXHavePassword(filepath: string, password: string) {
  try {
    const workbook = await XLSX_POPULATE.fromFileAsync(filepath, { password });
    const result = [];
    for (const sheet of workbook.sheets()) {
      result.push(
        XLSX.utils.sheet_to_json(
          XLSX.utils.aoa_to_sheet(sheet.usedRange().value()),
        ),
      );
    }
    return result;
  } catch (error) {
    console.log('error while extracting xlsx: ', error);
    return [];
  }
}

type ExcelSheetType = {
  name: string;
  data: any;
};

export function simpleWriteXLSX(filename: string, sheets: ExcelSheetType[]) {
  console.log("sheets: ");
  console.log("sheets: ", sheets[0].name, filename);
  const wb = XLSX.utils.book_new();
  for (const sheet of sheets) {
    XLSX.utils.book_append_sheet(
      wb,
      XLSX.utils.json_to_sheet(sheet.data),
      sheet.name
    );
  }
  console.log("writing file : ", filename);
  return XLSX.write(wb, { type: "buffer" });
}

export function writeXLSX(filename: string, sheets: ExcelSheetType[]) {
  if (sheets[0]) console.log("sheets: ", sheets[0].name, filename);
  const wb = XLSX.utils.book_new();
  for (const sheet of sheets) {
    const ws = XLSX.utils.json_to_sheet(sheet.data);
    ws["!rows"] = [{ hidden: true }]; // Hide the first row

    // Add bold formatting to row 6
    const boldStyle = { font: { bold: true } };
    const rowIndex = 5; // Row 6 in 0-based index
    const colCount = sheet.data[0] ? Object.keys(sheet.data[0]).length : 0;

    for (let colIndex = 0; colIndex < colCount; colIndex++) {
      const cellAddress = XLSX.utils.encode_cell({ r: rowIndex, c: colIndex });
      if (!ws[cellAddress]) {
        ws[cellAddress] = { t: "s", v: "" }; // Ensure the cell exists
      }
      ws[cellAddress].s = boldStyle;
    }

    // Calculate column widths
    const colWidths = sheet.data.reduce((widths, row) => {
      Object.keys(row).forEach((key, index) => {
        const value = row[key] ? row[key].toString() : "";
        widths[index] = Math.max(widths[index] || 10, value.length);
      });
      return widths;
    }, []);

    ws["!cols"] = colWidths.map((width) => ({ wch: width }));

    XLSX.utils.book_append_sheet(wb, ws, sheet.name);
  }
  console.log("writing file : ", filename);
  return XLSX.write(wb, { type: "buffer" });
}

export async function writeXLSXStream(filename: string, sheets: any[]) {
  let totalRows = 0;
  const options = {
    filename,
    useStyles: true,
    useSharedStrings: true,
  };
  const workbook = new ExcelJS.stream.xlsx.WorkbookWriter(options);

  for (const sheet of sheets) {
    const worksheet = workbook.addWorksheet(sheet.name);
    for (const row of sheet.data) {
      worksheet.addRow(row).commit();
      totalRows++;
    }
  }

  await workbook.commit();

  console.log(`Wrote ${totalRows} rows to ${filename}`);

  return fs.createReadStream(filename);
}

const ec = (r, c) => {
  return XLSX.utils.encode_cell({ r: r, c: c });
};
export function deleteWorksheetRow(ws, row_index) {
  const range = XLSX.utils.decode_range(ws['!ref']);
  for (let R = row_index; R < range.e.r; ++R) {
    for (let C = range.s.c; C <= range.e.c; ++C) {
      ws[ec(R, C)] = ws[ec(R + 1, C)];
    }
  }
  range.e.r--;
  ws['!ref'] = XLSX.utils.encode_range(range.s, range.e);
}

function _writeXLSXWithPassword({
  data = [{ data: [], name: 'Sheet1' }],
  password,
  filename,
  cb,
}) {
  XLSX_POPULATE.fromBlankAsync().then((wb) => {
    try {
      data.map((sheet) => {
        const ws = XLSX.utils.json_to_sheet(sheet.data);
        delete ws['!ref'];
        wb.addSheet(sheet.name);
        Object.keys(ws).map((cell) => {
          wb.sheet(sheet.name).cell(cell).value(ws[cell].v);
        });
      });
      wb.deleteSheet('Sheet1');
      console.log('Writing excel file with password ', password);
      return wb
        .toFileAsync(filename || `${getMoment().format('DDMMYY')}.xlsx`, {
          password,
        })
        .then(
          cb ||
            function () {
              console.log('done');
            },
        )
        .catch((err) => {
          console.log('error: ');
          (cb && cb(err)) || console.log(err);
        });
    } catch (err) {
      (cb && cb(err)) || console.log(err);
    }
  });
}

export async function writeXLSXWithPassword(params: {
  data: ExcelSheetType[];
  password: string;
  filename: string;
  cb?: any;
}): Promise<boolean> {
  const { data, password, filename, cb } = params;
  return new Promise((resolve, reject) => {
    _writeXLSXWithPassword({
      data,
      password,
      filename,
      cb: cb
        ? cb
        : (err) => {
            if (err) {
              console.log(err);
              return reject(err);
            }
            console.log(`Done ${filename} - ${password}`);
            resolve(true);
          },
    });
  });
}

export function parseCSV(
  filePath: string,
  mapHeaderCb?: any,
  formatDataCb?: any,
): Promise<any[]> {
  const result = [];
  return new Promise((resolve) => {
    fs.createReadStream(filePath)
      .pipe(
        csv({
          mapHeaders:
            mapHeaderCb ||
            function ({ header }) {
              return header.trim().toItemUid();
            },
        }),
      )
      .on('data', (data) => {
        result.push((formatDataCb && formatDataCb(data)) || data);
      })
      .on('end', () => {
        resolve(result);
      });
  });
}
