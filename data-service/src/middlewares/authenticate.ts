import { httpResponse, request } from "@helpers";
import { configService } from "@helpers";
import { DynamoDBService } from "../services/dynamodb.service";

const db = new DynamoDBService();

export default function auth(handler, isCDAS = false) {
  return async function (event, context) {
    try {

      let data = {
        method: "GET",
        url: configService.get("AUTH_URL"),
        token: event.headers.Authorization,
      };
      let result = await request(data);
      if (result.status !== 200) throw new Error("Not authenticated");

      const { user } = result.data;
      user.jwt = event.headers.Authorization;

      // Add the user to the context so it can be accessed in the handler
      context.user = user;
      context.isCDAS = !user.company;

      // await checkLicensing(event, context);

      // Call the handler
      return await handler(event, context);
    } catch (e: any) {
      if (e.response && e.response.data) {
        return httpResponse(e.response.status, e.response.data);
      }
      throw new Error(e.message);
    }
  };
}

  
async function checkLicensing(event, context) {
  console.log("LOG-event checkLicensing", event);
  try {
    const needsLicenseServices = {
      journey_history: [
        "truck_journey_history",
        "truck_journey_history_request",
      ],
      message_history: [],
    };
    const { user } = context;
    if (context.isCDAS) return;

    
    const data = await db.licensing().get({ company_id: user.company });
    if (data.items.length === 0) throw new Error("Your company does not have a license.");
    const license = data.items[0];
    // check service license
    if (!event.queryStringParameters) return;
    const { service } = event.queryStringParameters;

    if (
      !needsLicenseServices.journey_history.includes(service) &&
      !needsLicenseServices.message_history.includes(service)
    )
      return;

    if (needsLicenseServices.journey_history.includes(service)) {
      const serviceLicense = license.permissions.find((p) => p.code === 'journey_history' && p.status === 1);
      if (!serviceLicense) throw new Error("Service journey not allowed");
    }
    
    if (needsLicenseServices.message_history.includes(service)) { 
      const serviceLicense = license.permissions.find((p) => p.code === 'message_history' && p.status === 1);
      if (!serviceLicense) throw new Error("Service message not allowed");
    }
      // check date license
      const now = new Date();
    if (new Date(license.end_date) < now) throw new Error("License expired");
  } catch (e) {
    if (e.response && e.response.data) {
      return httpResponse(e.response.status, e.response.data);
    }
    throw new Error(e.message);
  }
}