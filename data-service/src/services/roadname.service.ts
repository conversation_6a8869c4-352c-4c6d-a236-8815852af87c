import { configService, getToken, request } from "../helpers";

export class RoadNameService {
  constructor() {}

  async getRoadName(data: {latitude: string, longitude: string}): Promise<string> {
    try {
		const token = await getToken();
		let roadNameRequest = {
			method: "POST",
			url: `${configService.get("ROAD_NAME_URL")}/road-map/name`,
			token,
			body: {
				latitude: `${data.latitude}`,
				longitude: `${data.longitude}`,
			},
		};
		let resultRoadName = await request(roadNameRequest);
		const roadName = resultRoadName.data?.name ? resultRoadName.data.name : "NOT_FOUND";
		return roadName;
    } catch (error) {
    //   console.error("Error fetching road name:", error);
      throw error;
    }
  }
}