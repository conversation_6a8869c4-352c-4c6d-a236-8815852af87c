import {
	CopyObjectCommand,
	DeleteObjectCommand,
	GetObjectCommand,
	HeadObjectCommand,
	ListObjectsV2Command,
	PutObjectCommand, // Add the missing import
	S3Client,
} from "@aws-sdk/client-s3"; // Update the import path
import fs from "fs";
import path from "path";
import ConfigService, { configService } from "@helpers/config";
import { getSignedUrl } from "@aws-sdk/s3-request-presigner"; // Import the getSignedUrl function

export class S3Service {
  private S3: S3Client;
  private source: string;
  constructor(source?: string) {
    this.source = source ? source + "_" : "";
    this.S3 = this.getS3Client(false);
  }

  getS3Client(force = false): S3Client {
    if (!force && this.S3) return this.S3;
    this.S3 = new S3Client({
      region: process.env.REGION || "ap-southeast-1",
      credentials: {
        accessKeyId:
          ConfigService.getInstance().get(`${this.source}S3_ACCESS_KEY`),
        secretAccessKey:
          ConfigService.getInstance().get(`${this.source}S3_SECRET_KEY`),
      },
    });
    return this.S3;
  }
  getImageUrl(fileByPath) {
    return `https://${
      ConfigService.getInstance().get(`${this.source}S3_BUCKET`) ||
      "ctr-report-service"
    }.s3-${
      ConfigService.getInstance().get("S3_REGION") || "ap-southeast-1"
    }.amazonaws.com/${fileByPath}`;
  }
  async moveFile(oldPath, newPath, publicRead = false) {
    try {
      const s3Client = this.getS3Client();
      const command = new CopyObjectCommand({
        Bucket: ConfigService.getInstance().get(`${this.source}S3_BUCKET`),
        CopySource: `${ConfigService.getInstance().get(
          `${this.source}S3_BUCKET`
        )}/${oldPath}`,
        Key: newPath,
        ACL: publicRead ? "public-read" : undefined,
        ServerSideEncryption: "AES256",
      });
      const response = await s3Client.send(command);
      const deleteCommand = new DeleteObjectCommand({
        Bucket: ConfigService.getInstance().get(`${this.source}S3_BUCKET`),
        Key: oldPath,
      });
      await s3Client.send(deleteCommand);
      return response;
    } catch (error) {
      return false;
    }
  }
  async checkIfFileExists(filePath) {
    try {
      const s3Client = this.getS3Client();
      const command = new HeadObjectCommand({
        Bucket: ConfigService.getInstance().get(`${this.source}S3_BUCKET`),
        Key: filePath,
      });
      return await s3Client.send(command);
    } catch (error) {
      return false;
    }
  }
  async listFilesInFolder(folderPath) {
    const params = new ListObjectsV2Command({
      Bucket: ConfigService.getInstance().get(`${this.source}S3_BUCKET`),
      Prefix: folderPath,
    });

    try {
      const response = await this.getS3Client().send(params);
      const files = response.Contents;

      // Extract the file names from the response
      return files.filter((f) => f.Size).map((file) => file.Key);
    } catch (error) {
      console.error("Error:", error);
      return [];
    }
  }
  async uploadXLSX({ key, filePath, publicRead = false }) {
    try {
      const s3Client = this.getS3Client();
      const command = new PutObjectCommand({
        ACL: publicRead ? "public-read" : undefined,
        Bucket: ConfigService.getInstance().get(`${this.source}S3_BUCKET`),
        Body: fs.readFileSync(filePath),
        Key: key,
        ServerSideEncryption: "AES256",
        ContentType:
          "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
      });
      const response = await s3Client.send(command);
      console.log("Uploaded");
      return response;
    } catch (error) {
      console.log("uploadXLSX", error);
      return null;
    }
  }
  async downloadFileFromS3(key, downloadPath) {
    const command = new GetObjectCommand({
      Bucket: ConfigService.getInstance().get(`${this.source}S3_BUCKET`),
      Key: key,
    });

    try {
      const response = await this.getS3Client().send(command);

      // Create a writable stream to save the file
      const fileStream = fs.createWriteStream(downloadPath);

      // Pipe the response stream to the file stream
      // eslint-disable-next-line @typescript-eslint/ban-ts-comment
      // @ts-expect-error
      response.Body.pipe(fileStream);

      // Wait for the file to finish writing
      await new Promise((resolve, reject) => {
        fileStream.on("finish", resolve);
        fileStream.on("error", reject);
      });

      console.log("File downloaded successfully!");
      return true;
    } catch (error) {
      console.error("Error:", error);
      return false;
    }
  }

  async uploadFile({ publicRead = true, key, fileBuffer }) {
    try {
      const s3Client = this.getS3Client();
      const bucketName =
        ConfigService.getInstance().get(`${this.source}S3_BUCKET`) || "ctr-report-service";
      const command = new PutObjectCommand({
        // ACL: publicRead ? "public-read" : undefined,
        Bucket: bucketName,
        Body: fileBuffer,
        Key: key,
        ContentType: this.getContentType(path.extname(key)),
      });
      const response = await s3Client.send(command);
      // Create a GetObjectCommand

      const getCommand = new GetObjectCommand({
        Bucket: bucketName,
        Key: key,
      });

      // Generate the pre-signed URL
      const url = await getSignedUrl(s3Client, getCommand, {
        expiresIn: configService.getNumber('EXPIRE_SECONDS') || 60,
      }); // URL expires in 5 minutes

      return url;
    } catch (error) {
      console.log("uploadFile", error);
      return error;
    }
  }

  async uploadFileStream({ publicRead = true, key, fileStream }) {
    try {
      const s3Client = this.getS3Client();
      const bucketName =
        ConfigService.getInstance().get(`${this.source}S3_BUCKET`) || "ctr-report-service";
      const command = new PutObjectCommand({
        // ACL: publicRead ? "public-read" : undefined,
        Bucket: bucketName,
        Body: fileStream,
        Key: key,
        ContentType: this.getContentType(path.extname(key)),
      });
      const response = await s3Client.send(command);
      // Create a GetObjectCommand

      // Generate the pre-signed URL
      const url = await getSignedUrl(
        s3Client,
        new GetObjectCommand({
          Bucket: bucketName,
          Key: key,
        }),
        {
          expiresIn: configService.getNumber('EXPIRE_SECONDS') || 60,
        }
      ); // URL expires in 24 hours

      return url;
    } catch (error) {
      console.log("uploadFile", error);
      return error;
    }
  }

  async getUrl(key, expiration) {
    try {
      const s3Client = this.getS3Client();
      const command = new GetObjectCommand({
        Bucket: ConfigService.getInstance().get(`${this.source}S3_BUCKET`),
        Key: key,
      });
      const url = await getSignedUrl(s3Client, command, {
        expiresIn: expiration,
      });
      return url;
    } catch (error) {
      console.log("getSignedUrl", error);
      return null;
    }
  }

  getContentType(fileExtension) {
    return (
      {
        ".jpg": "image/jpeg",
        ".jpeg": "image/jpeg",
        ".png": "image/png",
        ".gif": "image/gif",
        ".pdf": "application/pdf",
        ".doc": "application/msword",
        ".docx":
          "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
        ".xls": "application/vnd.ms-excel",
        ".xlsx":
          "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
        ".ppt": "application/vnd.ms-powerpoint",
        ".pptx":
          "application/vnd.openxmlformats-officedocument.presentationml.presentation",
        ".txt": "text/plain",
        ".csv": "text/csv",
        ".mp4": "video/mp4",
        ".mp3": "audio/mpeg",
        ".wav": "audio/wav",
        ".zip": "application/zip",
        ".tar": "application/x-tar",
      }[fileExtension] || "application/octet-stream"
    );
  }
}
