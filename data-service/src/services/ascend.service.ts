import { request } from "@helpers";
import { configService } from "../helpers/config";

const key =
  configService.get("ASCEND_KEY") ||
  "$2a$11$koUAZo1Cb4B1vuwDAS0qU.M.vmf/oy.JCMiWnC6xd3SHZP7g0sZpy";

const getHistoricalData = async ({ truck_number, start, end }) => {
  // handle case start is Thu Jan 25 2024 06:52:41 GMT+0000 (Coordinated Universal Time)
  if (typeof start === "object") {
    start = Math.floor(start.getTime() / 1000);
  }
  // handle case end is Thu Jan 25 2024 06:52:41 GMT+0000 (Coordinated Universal Time)
  if (typeof end === "object") {
    end = Math.floor(end.getTime() / 1000);
  }

  // handle start is 2024-01-25T06:52:41.000Z
  if (start.toString().includes("T")) {
    start = Math.floor(new Date(start).getTime() / 1000);
  }
  // handle end is 2024-01-25T06:52:41.000Z
  if (end.toString().includes("T")) {
    end = Math.floor(new Date(end).getTime() / 1000);
  }

  // handle start is 2024-01-25
  if (start.toString().includes("-")) {
    start = Math.floor(new Date(start).getTime() / 1000);
  }
  // handle end is 2024-01-25
  if (end.toString().includes("-")) {
    end = Math.floor(new Date(end).getTime() / 1000);
  }

  // handle start is a number and is milliseconds
  if (typeof start === "number" && start.toString().length === 13) {
    start = Math.floor(start / 1000);
  }
  // handle end is a number and is milliseconds
  if (typeof end === "number" && end.toString().length === 13) {
    end = Math.floor(end / 1000);
  }

  const url = `https://ext.logistics.myascents.net/api/3rdparty/unit/historical2?key=${key}&name=${truck_number}&start=${start}&end=${end}`;
  console.log('LOG-urlas', url);
  const response = await request({ method: "GET", url });
  return response;
}; 

export default {
  getHistoricalData
}