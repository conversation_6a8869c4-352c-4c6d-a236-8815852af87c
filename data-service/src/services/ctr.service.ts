import { getToken, request } from "@helpers";
import { configService } from "@helpers/config";

const EMAIL_TEMPLATE = {
  report: `
Dear <Receiver>,<br>
<br>
Your journey history is now ready to download.<br>
You can <Link> here - this link is valid till <Expire> <br>
You may also head to <Login> to download this report should the link be expired, or view your past requests.
<br>
<br>
Thank you,
<br>
Yours Sincerely,<br>
CTR`,
};

export class CTRService {
  constructor() {}

  async isValidTruck(
    truckNumbers: any[],
    belongCompany: string,
    token: string
  ) {
    try {
      const vehicleNos = truckNumbers
        .map((number) => `vehicleNo[$in][]=${number}`)
        .join("&");
      const result = await request({
        method: "GET",
        url: `${configService.get("CTR_URL")}/vehicles?${vehicleNos}${
          belongCompany === "CDAS" ? "" : `&company=${belongCompany}`
        }`,
        token,
      });
      console.info("LOG-result.data", result.data);
      if (result.data.total !== truckNumbers.length) return false;
    } catch (error) {
      console.error("isValidTruck", error);
      return false;
    }
    return true;
  }

  async sendEmail(data: {emailReceiver: string[], subject: string, emailContent: string}) {
    const token = await getToken();
    let sendNotifyRequest = {
      method: "POST",
      url: `${configService.get("CTR_URL")}/custom-notification`,
      token,
      body: {
        from: "<EMAIL>",
        receiver: `${data.emailReceiver.join(",")}`,
        // receiver: `<EMAIL>`,
        type: "email",
        attachements: "dGhpcyBpcyB0aGFuZw==",
        filename: "filename",
        subject: data.subject,
        html: data.emailContent,
      },
    };
    const response = await request(sendNotifyRequest);
    return response.data;
  }

  async sendReportCompleted(data: {receiver: {fullname: string, email: string}, link: string}) {
    console.log('LOG-sendReportCompleted');
    const expiredAt = new Date(
      Date.now() + configService.getNumber("EXPIRE_SECONDS") * 1000
    ).toLocaleString("en-SG", { timeZone: "Asia/Singapore" });
    const emailContent = EMAIL_TEMPLATE.report
      .replace("<Receiver>", data.receiver.fullname)
      .replace("<Link>", `<a href="${data.link}"> download the report </a>`)
      .replace("<Expire>", expiredAt)
      .replace(
        "<Login>",
        `<a href="${configService.get("CTR_PORTAL_URL")}"> login </a>`
      );
    return await this.sendEmail({
      emailReceiver: [data.receiver.email], 
      // emailReceiver: ['<EMAIL>'], 
      subject: "Your journey history is now ready to download", 
      emailContent
    });
  }



}


