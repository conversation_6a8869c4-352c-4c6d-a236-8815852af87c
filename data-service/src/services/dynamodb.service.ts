import { DynamoDB } from "aws-sdk";
import config, { configService } from "@helpers/config";

export class DynamoDBService {
  private client: DynamoDB.DocumentClient;

  constructor() {
    this.client = new DynamoDB.DocumentClient();
  }

  public truckJourneyHistory() {
    const tableName = configService.get("TABLE_TRUCK_JOURNEY_HISTORY");
    return this.getTableMethods(tableName);
  }

  public session() {
    const tableName = configService.get("TABLE_SESSION");
    return this.getTableMethods(tableName);
  }

  public licensing() {
    const tableName = configService.get("TABLE_LICENSING");
    return this.getTableMethods(tableName);
  }

  public ascend() {
    const tableName = configService.get("TABLE_ASCEND");
    return this.getTableMethods(tableName);
  }
  
  public repairing() {
    const tableName = configService.get("TABLE_REPAIRING");
    return this.getTableMethods(tableName);
  }

  // Add more methods for other tables.
  private getTableMethods(tableName: string) {
    return {
      getAll: async () => {
        const params = {
          TableName: tableName,
        };
        const result = await this.client.scan(params).promise();

        return {
          items: result.Items,
          lastEvaluatedKey: result.LastEvaluatedKey,
        };
      },

      get: async (params) => {
        let filterExpression = "";
        const expressionAttributeValues = {};
        for (const property in params) {
          if (params[property]) {
          if (filterExpression) filterExpression += " AND ";
          filterExpression += `#${property} = :${property}`;
          expressionAttributeValues[`:${property}`] = params[property];
          }
        }

        params = {
          FilterExpression: filterExpression,
          ExpressionAttributeNames: Object.keys(params).reduce(
          (result, key) => {
            if (params[key]) {
            result[`#${key}`] = key;
            }
            return result;
          },
          {}
          ),
          ExpressionAttributeValues: expressionAttributeValues,
          TableName: tableName,
        };

        const result = await this.client.scan(params).promise();

        return {
          items: result.Items,
          lastEvaluatedKey: result.LastEvaluatedKey,
        };
      },

      put: async (params) => {
        params = {
          Item: {
          ...params,
          },
          TableName: tableName,
        };

        const result = await this.client.put(params).promise();
        return result;
      },
    };
  }
}
