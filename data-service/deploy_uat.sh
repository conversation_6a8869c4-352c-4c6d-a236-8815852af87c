#!/bin/bash
env="DEV"
env_file=".env.development"
service_name="DataService"

yarn build

# update-function-configuration with the new environment variables
# Read all variables from the environment file
variables=""
while IFS='=' read -r key value
do
	variables+="${key}=${value},"
done < "$env_file"

# Update the lambda function configuration with the new environment variables
aws lambda update-function-configuration --function-name "$service_name" --environment "Variables={$variables}" --no-cli-pager

# Specify the output zip file name
zip_file="dist/function.zip"

echo "Creating a new zip file: $zip_file"
# Update the lambda function with the new code
aws lambda update-function-code --function-name "$service_name" --zip-file fileb://"$zip_file" --no-cli-pager

echo "Updated the lambda function with the new code"
# Add a delay
echo "Waiting for the update to complete before publishing a new version"
sleep 10

# Publish a new version
version=$(aws lambda publish-version --function-name "$service_name" --description "develop" --query 'Version' --output text)
echo "Published new version: $version"

# Update the alias to point to the new version
aws lambda update-alias --function-name "$service_name" --name "$env" --function-version "$version" --no-cli-pager