export const event = {
  resource: "/data",
  path: "/data",
  httpMethod: "GET",
  headers: {
    Accept: "*/*",
    "Accept-Encoding": "gzip, deflate, br",
    Authorization:
      "eyJhbGciOiJIUzI1NiIsInR5cCI6ImFjY2VzcyJ9.eyJvYXQiOjE3MTk4MDU5NjUsImlhdCI6MTcxOTgwNTk2NSwiZXhwIjoxNzE5OTc4NzY1LCJhdWQiOiJodHRwczovL3lvdXJkb21haW4uY29tIiwiaXNzIjoiZmVhdGhlcnMiLCJzdWIiOiI2MTE5Y2M3NTJjMjBlNzA5MTkyM2M2MmIiLCJqdGkiOiJhMjY2OWJhMS02OWE1LTQ2NTEtYWUzOS1kZjk0MDI0MDFiYTAifQ.i_lwyaYTjMJNfnXlOoJHBSwyRqp33Uwf_Y3Fm9M0wjk",
    "Cache-Control": "no-cache",
    Host: "adjnrc6vf7.execute-api.ap-southeast-1.amazonaws.com",
    "Postman-Token": "eb233e68-7b31-4c40-8846-39309106d817",
    "User-Agent": "PostmanRuntime/7.39.0",
    "X-Amzn-Trace-Id": "Root=1-66822d6f-56173ac424918da219d09b1b",
    "X-Forwarded-For": "**************",
    "X-Forwarded-Port": "443",
    "X-Forwarded-Proto": "https",
  },
  multiValueHeaders: {
    Accept: ["*/*"],
    "Accept-Encoding": ["gzip, deflate, br"],
    Authorization: [
      "eyJhbGciOiJIUzI1NiIsInR5cCI6ImFjY2VzcyJ9.eyJvYXQiOjE3MTk4MDU5NjUsImlhdCI6MTcxOTgwNTk2NSwiZXhwIjoxNzE5OTc4NzY1LCJhdWQiOiJodHRwczovL3lvdXJkb21haW4uY29tIiwiaXNzIjoiZmVhdGhlcnMiLCJzdWIiOiI2MTE5Y2M3NTJjMjBlNzA5MTkyM2M2MmIiLCJqdGkiOiJhMjY2OWJhMS02OWE1LTQ2NTEtYWUzOS1kZjk0MDI0MDFiYTAifQ.i_lwyaYTjMJNfnXlOoJHBSwyRqp33Uwf_Y3Fm9M0wjk",
    ],
    "Cache-Control": ["no-cache"],
    Host: ["adjnrc6vf7.execute-api.ap-southeast-1.amazonaws.com"],
    "Postman-Token": ["eb233e68-7b31-4c40-8846-39309106d817"],
    "User-Agent": ["PostmanRuntime/7.39.0"],
    "X-Amzn-Trace-Id": ["Root=1-66822d6f-56173ac424918da219d09b1b"],
    "X-Forwarded-For": ["**************"],
    "X-Forwarded-Port": ["443"],
    "X-Forwarded-Proto": ["https"],
  },
  queryStringParameters: {
    company: "CDAS",
    end: "**********",
    service: "truck_journey_history",
    start: "**********",
    truck_numbers:
      "XD1086R,XD1523X,XD1660H,XD1668L,XD1792K,XD2830C,XD5286D,XD8395C,XD8677P,XE1666L",
  },
  multiValueQueryStringParameters: {
    company: ["CDAS"],
    end: ["**********"],
    service: ["truck_journey_history"],
    start: ["**********"],
    truck_numbers: [
      "XD1086R,XD1523X,XD1660H,XD1668L,XD1792K,XD2830C,XD5286D,XD8395C,XD8677P,XE1666L",
    ],
  },
  pathParameters: null,
  stageVariables: null,
  requestContext: {
    resourceId: "yqwhk3",
    resourcePath: "/data",
    httpMethod: "GET",
    extendedRequestId: "aNwJZEnISQ0EBGQ=",
    requestTime: "01/Jul/2024:04:15:43 +0000",
    path: "/dev/data",
    accountId: "************",
    protocol: "HTTP/1.1",
    stage: "dev",
    domainPrefix: "adjnrc6vf7",
    requestTimeEpoch: *************,
    requestId: "9a07937c-0521-494d-aa3f-4346e746a0ea",
    identity: {
      cognitoIdentityPoolId: null,
      accountId: null,
      cognitoIdentityId: null,
      caller: null,
      sourceIp: "**************",
      principalOrgId: null,
      accessKey: null,
      cognitoAuthenticationType: null,
      cognitoAuthenticationProvider: null,
      userArn: null,
      userAgent: "PostmanRuntime/7.39.0",
      user: null,
    },
    domainName: "adjnrc6vf7.execute-api.ap-southeast-1.amazonaws.com",
    deploymentId: "9hjx74",
    apiId: "adjnrc6vf7",
  },
  body: null,
  isBase64Encoded: false,
};
        const context =  {
            "callbackWaitsForEmptyEventLoop": true,
            "functionVersion": "$LATEST",
            "functionName": "DataService",
            "memoryLimitInMB": "10240",
            "logGroupName": "/aws/lambda/DataService",
            "logStreamName": "2024/07/01/[$LATEST]8d9a23a6aa8f467babd7ef7028dfc0a3",
            "invokedFunctionArn": "arn:aws:lambda:ap-southeast-1:************:function:DataService",
            "awsRequestId": "e5796c71-d7f6-4eeb-90c5-defc5dafe156"
        }