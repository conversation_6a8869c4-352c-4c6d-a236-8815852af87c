
const axios = require("axios");


const request = async (data) => {
  /* ... */
  let options = {
    method: data.method,
    url: data.url,
    headers: {
      Authorization: data.token || null,
      "content-type": "application/json",
      "cache-control": "no-cache",
    },
    data: data.body,
  };
  return await axios(options);
};

async function test() { 
	const result = await request({
		method: "GET",
		url: `https://psa-ws-eservices.cdaslink.sg/vehicles`,
	    token: `eyJhbGciOiJIUzI1NiIsInR5cCI6ImFjY2VzcyJ9.eyJvYXQiOjE3MjMzNzA5OTgsImlhdCI6MTcyMzM3MDk5OCwiZXhwIjoxNzIzOTc1Nzk4LCJhdWQiOiJodHRwczovL3lvdXJkb21haW4uY29tIiwiaXNzIjoiZmVhdGhlcnMiLCJzdWIiOiI2MmRiNjQwZWFhYjAwYjRlOTVhZjA5NTIiLCJqdGkiOiIwNjFmMjI4MS0yYmMzLTRiMzgtYjgyYi03ZDRlYzdlMmUyZTUifQ.aFACiG-b4N0FK5Qp1y-HtJu3WUl01zdTywkxGmeIl7M`,
	});

	const stringV = result.data.data.splice(0, 100).map((item) => item.vehicleNo).join(',')

	// request to https://ht59uksfgf.execute-api.ap-southeast-1.amazonaws.com/prod/data?start=1717952400&company=CDAS&truck_numbers=stringV&service=truck_journey_history&end=1717994330

	console.log('LOG-stringV', stringV);
	const result2 = await request({
		method: "GET",
		url: `https://ht59uksfgf.execute-api.ap-southeast-1.amazonaws.com/prod/data?start=1722963600&company=CDAS&truck_numbers=${stringV}&service=truck_journey_history&end=1723049940`,
		token: `eyJhbGciOiJIUzI1NiIsInR5cCI6ImFjY2VzcyJ9.eyJvYXQiOjE3MjMzNzA5OTgsImlhdCI6MTcyMzM3MDk5OCwiZXhwIjoxNzIzOTc1Nzk4LCJhdWQiOiJodHRwczovL3lvdXJkb21haW4uY29tIiwiaXNzIjoiZmVhdGhlcnMiLCJzdWIiOiI2MmRiNjQwZWFhYjAwYjRlOTVhZjA5NTIiLCJqdGkiOiIwNjFmMjI4MS0yYmMzLTRiMzgtYjgyYi03ZDRlYzdlMmUyZTUifQ.aFACiG-b4N0FK5Qp1y-HtJu3WUl01zdTywkxGmeIl7M`,
	});

	console.log(result2.data)
}

test();