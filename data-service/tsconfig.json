{
    "compileOnSave": false,
    "compilerOptions": {
        "target": "es2017",
        "lib": [
            "es2021",
            "esnext.asynciterable"
        ],
        "types": ["node"],
        "typeRoots": [
            "node_modules/@types"
        ],
        "allowSyntheticDefaultImports": true,
        "experimentalDecorators": true,
        "emitDecoratorMetadata": true,
        "forceConsistentCasingInFileNames": true,
        "moduleResolution": "node",
        "module": "commonjs",
        "pretty": true,
        "sourceMap": true,
        "outDir": "dist",
        "allowJs": true,
        "noEmit": false,
        "esModuleInterop": true,
        "resolveJsonModule": true,
        "importHelpers": true,
        "baseUrl": ".",
        "paths": {
            "@src/*": ["*"],
            "@helpers/*": ["src/helpers/*"],
            "@handlers/*": ["src/handlers/*"],
            "@services/*": ["src/services/*"],
            "@middlewares/*": ["src/middlewares/*"],
            "@helpers": ["src/helpers"]
        }
    },
    "include": [
        "src/**/*.json",
        "src/**/*.ts" // Add a valid input file or directory here
, "index.ts", "src/middlewares"    ],
    "exclude": [
        "node_modules"
    ]
}