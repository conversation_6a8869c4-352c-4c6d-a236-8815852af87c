#!/bin/bash

FUNCTION_NAME="DataService"

# Get all versions
all_versions=$(aws lambda list-versions-by-function --function-name $FUNCTION_NAME --query 'Versions[*].[Version]' --output text)

echo "All versions: $all_versions"

# Get all aliases
all_aliases=$(aws lambda list-aliases --function-name $FUNCTION_NAME --query 'Aliases[*].[FunctionVersion]' --output text)

echo "All aliases: $all_aliases"

# Loop through all versions
for version in $all_versions
do
    # Check if the version is in the list of aliases
    if [[ ! " $all_aliases " =~ " $version " ]]; then
        echo "Deleting version: $version"
        # If the version is not associated with an alias, delete it
        aws lambda delete-function --function-name $FUNCTION_NAME --qualifier $version
    fi
done