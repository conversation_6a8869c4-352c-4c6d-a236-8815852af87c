import {
  Context,
  APIGatewayProxyResult,
  APIGatewayProxyEvent,
} from "aws-lambda";
import truckJourneyHistoryHandler from "@src/src/handlers/truck_journey_history.handler";
import truckJourneyHistoryRequestHandler from "@src/src/handlers/truck_journey_history_request.handler";
import {linkDataServiceHandler, getAuthTokenHandler, validateTokenHandler} from "@src/src/handlers/ctr_integration.handler";
import generateS3UrlHandler from "@src/src/handlers/generate_s3_url.handler";
import licensing from "@src/src/handlers/licensing.handler";
import ascendPullHandler from "@src/src/handlers/pull_ascend_data.handler";
import cronjobRoadnameHandler from "@src/src/handlers/cronjob_roadname.handler";

import { httpResponse } from "@helpers/response";
import { DynamoDBService } from "@services/dynamodb.service";
import auth from "@middlewares/authenticate";

const db = new DynamoDBService();

const handlers = {
  "/data": {
    truck_journey_history: {
      GET: auth(truckJourneyHistoryHandler),
    },
    truck_journey_history_request: {
      GET: auth(truckJourneyHistoryRequestHandler),
    },
    generate_s3_url: {
      GET: auth(generateS3UrlHandler),
    },

    // auth
    link_data_service: {
      GET: auth(linkDataServiceHandler),
    },
    get_auth_token: {
      GET: getAuthTokenHandler,
    },
    validate_token: {
      GET: auth(validateTokenHandler),
    },

    // ascend
    pull_data: {
      GET: ascendPullHandler,
    },

    road_name: {
      GET: cronjobRoadnameHandler,
    },
  },
  "/licensing": {
    licensing: {
      GET: auth(licensing.getHandler),
      POST: auth(licensing.createHandler),
      PUT: auth(licensing.updateHandler),
    },
  },
};

export const handler = async (
  event: APIGatewayProxyEvent,
  context: Context
): Promise<APIGatewayProxyResult> => {
  try {
    if (event.resources && 
      event.resources[0] ==
      "arn:aws:events:ap-southeast-1:147722175163:rule/pull-ascend-1AM-everyday"
    ) {
      return await ascendPullHandler(event, context);
    } 
    
    if (
      event.resources &&
      event.resources[0] ==
        "arn:aws:events:ap-southeast-1:147722175163:rule/add-roadname-to-ascend-2AM-everyday"
    ) {
      return await cronjobRoadnameHandler(event);
    } 
    const { httpMethod, resource } = event;
    console.log('LOG-event', event);
    if ("Records" in event) {
      let handler = null;
      console.log('LOG-SNS', event.Records[0].Sns.Message);
      const handlerString = JSON.parse(event.Records[0].Sns.Message).handler;
      switch (handlerString) {
        case "truck_journey_history":
          handler = truckJourneyHistoryHandler;
          break;
        case "pull_ascend_data":
          handler = ascendPullHandler;
          break;
        case "cronjob_roadname":
          handler = cronjobRoadnameHandler;
          break;
        default:
          break;
      }
      console.log('LOG-handlerString', handlerString);
      return await handler(event, context);
    }
    const service =
      event.queryStringParameters?.service || resource?.replace("/", "");
    const handler = handlers[resource][service][httpMethod.toUpperCase()];
    if (!handler) {
      return httpResponse(400, { error: "Not found service" });
    }

    return await handler(event, context);
  } catch (error) {
    console.log("LOG-error", error);
    
    if (error.response) return httpResponse(error.response.data?.status || 500, {
      error: error.response.data?.msg,
    });
    return httpResponse(500, { error: error.message });
  }
};
