# Request Logging System

The EIR backend now includes a comprehensive request logging system that tracks all API requests from both mobile and portal applications.

## Overview

The request logging system captures detailed information about every API request including:

- **Request Details**: Method, endpoint, headers, query parameters, body payload
- **User Information**: User ID, username, company, role (when authenticated)
- **Response Details**: Status code, response data, response time
- **Client Information**: IP address, user agent
- **Success/Error Status**: Whether the request was successful or failed

## Features

### 🔍 Comprehensive Tracking
- Tracks all HTTP methods (GET, POST, PUT, DELETE, etc.)
- Captures request and response data
- Measures response time in milliseconds
- Identifies source (mobile vs portal)

### 🔒 Security & Privacy
- Automatically sanitizes sensitive data from logs
- Removes passwords, tokens, API keys from request bodies
- Strips authorization headers and cookies
- Limits response data size to prevent large logs

### 📊 Query & Analytics
- Built-in query methods for common use cases
- Database indexes for optimal performance
- Support for date range queries
- Error log filtering

## Database Schema

The request logs are stored in the `RequestLog` collection with the following structure:

```typescript
interface IRequestLog {
    request_id: string;           // Unique identifier for each request
    source: 'mobile' | 'portal'; // Request source
    method: string;               // HTTP method (GET, POST, etc.)
    endpoint: string;             // API endpoint path
    full_url: string;             // Complete request URL
    ip_address: string;           // Client IP address
    user_agent: string;           // Client user agent
    headers: object;              // Request headers (sanitized)
    query_params: object;         // URL query parameters
    body_payload: object;         // Request body (sanitized)
    user_id?: ObjectId;           // User ID (if authenticated)
    user_info?: {                 // User details (if authenticated)
        username?: string;
        fullname?: string;
        company?: string;
        role?: string;
    };
    response_status: number;      // HTTP response status code
    response_data?: object;       // Response body (size limited)
    response_time_ms: number;     // Response time in milliseconds
    error_message?: string;       // Error message (if failed)
    success: boolean;             // Whether request was successful
    created_at: Date;             // Timestamp when log was created
}
```

## Usage

### Automatic Logging

The logging middleware is automatically applied to all routes:

- **Mobile routes** (`/api/*`): Logged with `source: 'mobile'`
- **Portal routes** (`/api/portal/*`): Logged with `source: 'portal'`

No additional configuration is required - all requests are automatically logged.

### Querying Logs

#### Basic Queries

```typescript
import models from '@models';

// Get recent logs
const recentLogs = await models.RequestLog.find({})
    .sort({ created_at: -1 })
    .limit(100);

// Get logs by source
const mobileLogs = await models.RequestLog.find({ source: 'mobile' });
const portalLogs = await models.RequestLog.find({ source: 'portal' });

// Get error logs
const errorLogs = await models.RequestLog.find({ success: false });

// Get logs for specific user
const userLogs = await models.RequestLog.find({ user_id: userId });
```

#### Using Static Methods

The model includes convenient static methods for common queries:

```typescript
// Get logs by user (last 100)
const userLogs = await models.RequestLog.getLogsByUser(userId, 100);

// Get logs by endpoint
const endpointLogs = await models.RequestLog.getLogsByEndpoint('/api/receipts');

// Get error logs
const errorLogs = await models.RequestLog.getErrorLogs(50);

// Get logs by source
const mobileLogs = await models.RequestLog.getLogsBySource('mobile', 100);

// Get logs by date range
const startDate = new Date('2024-01-01');
const endDate = new Date('2024-01-31');
const dateLogs = await models.RequestLog.getLogsByDateRange(startDate, endDate);
```

### Analytics Examples

#### Response Time Analysis

```typescript
// Average response time by endpoint
const avgResponseTime = await models.RequestLog.aggregate([
    { $group: {
        _id: '$endpoint',
        avgResponseTime: { $avg: '$response_time_ms' },
        requestCount: { $sum: 1 }
    }},
    { $sort: { avgResponseTime: -1 }}
]);
```

#### Error Rate Analysis

```typescript
// Error rate by endpoint
const errorRates = await models.RequestLog.aggregate([
    { $group: {
        _id: '$endpoint',
        totalRequests: { $sum: 1 },
        errorCount: { $sum: { $cond: ['$success', 0, 1] }}
    }},
    { $addFields: {
        errorRate: { $divide: ['$errorCount', '$totalRequests'] }
    }}
]);
```

#### User Activity Analysis

```typescript
// Most active users
const activeUsers = await models.RequestLog.aggregate([
    { $match: { user_id: { $exists: true }}},
    { $group: {
        _id: '$user_id',
        requestCount: { $sum: 1 },
        lastActivity: { $max: '$created_at' }
    }},
    { $sort: { requestCount: -1 }}
]);
```

## Performance Considerations

### Database Indexes

The following indexes are automatically created for optimal query performance:

- `created_at: -1` - For time-based queries
- `source: 1, created_at: -1` - For source-specific queries
- `user_id: 1, created_at: -1` - For user-specific queries
- `endpoint: 1, created_at: -1` - For endpoint-specific queries
- `success: 1, created_at: -1` - For error analysis
- `ip_address: 1, created_at: -1` - For IP-based analysis

### Data Retention

Consider implementing a data retention policy to manage log storage:

```typescript
// Example: Delete logs older than 90 days
const ninetyDaysAgo = new Date();
ninetyDaysAgo.setDate(ninetyDaysAgo.getDate() - 90);

await models.RequestLog.deleteMany({
    created_at: { $lt: ninetyDaysAgo }
});
```

## Security Features

### Data Sanitization

The logging system automatically removes sensitive information:

**Headers removed:**
- `authorization`
- `cookie`
- `x-api-key`
- `x-auth-token`

**Body fields removed:**
- `password`
- `token`
- `secret`
- `api_key`
- `private_key`

### Response Size Limiting

Response data is limited to 10KB to prevent excessive log storage. Larger responses are truncated with a `[truncated]` indicator.

## Testing

Run the test script to verify the logging system:

```bash
# Compile and run the test script
npm run build:tsc
node dist/scripts/testRequestLogging.js
```

This will:
1. Start a test server with logging middleware
2. Make sample requests to various endpoints
3. Verify logs are saved correctly
4. Test query methods and analytics

## Monitoring & Alerts

Consider setting up monitoring for:

- High error rates on specific endpoints
- Unusual response times
- Suspicious IP activity
- Failed authentication attempts

Example alert query for high error rate:

```typescript
const errorRate = await models.RequestLog.aggregate([
    { $match: { 
        created_at: { $gte: new Date(Date.now() - 3600000) } // Last hour
    }},
    { $group: {
        _id: null,
        total: { $sum: 1 },
        errors: { $sum: { $cond: ['$success', 0, 1] }}
    }},
    { $addFields: {
        errorRate: { $divide: ['$errors', '$total'] }
    }}
]);

if (errorRate[0]?.errorRate > 0.1) { // 10% error rate
    // Send alert
}
```
