{"name": "eir-backend", "version": "1.0.0", "description": "TypeScript + Mongoose + MongoDB + Express API Server", "author": "<PERSON>u <PERSON> <<EMAIL>>", "license": "MIT", "scripts": {"start": "nodemon", "dev": "NODE_ENV=development nodemon dist/server.js", "prod": "NODE_ENV=production node dist/server.js", "build": "swc src -d dist --copy-files --include-dotfiles", "watch": "swc src -d dist --copy-files -w --include-dotfiles", "build:tsc": "tsc && tsc-alias", "seed": "yarn build && node dist/scripts", "test": "yarn build && node dist/tests/index", "test-helpers": "yarn build && node dist/helpers/test/index"}, "dependencies": {"@aws-sdk/client-s3": "^3.617.0", "@aws-sdk/credential-providers": "^3.617.0", "@aws-sdk/s3-request-presigner": "^3.717.0", "@jimp/types": "^1.6.0", "@socket.io/redis-adapter": "^7.2.0", "@types/gm": "^1.25.4", "amqplib": "^0.10.5", "axios": "^1.2.1", "bcrypt": "^5.0.1", "class-transformer": "^0.5.1", "class-validator": "^0.13.1", "compression": "^1.7.4", "connect-mongodb-session": "^3.1.1", "cookie-parser": "^1.4.5", "cors": "^2.8.5", "cron": "^3.1.7", "csv-parser": "^3.0.0", "dotenv": "^10.0.0", "ejs": "^3.1.8", "envalid": "^7.1.0", "express": "^4.17.1", "express-session": "^1.17.3", "express-useragent": "^1.0.15", "express-validator": "^6.14.1", "faker": "5.5.3", "gm": "^1.25.0", "helmet": "^4.6.0", "hpp": "^0.2.3", "jsonwebtoken": "^8.5.1", "lodash": "^4.17.21", "method-override": "^3.0.0", "moment-timezone": "^0.5.34", "mongoose": "^6.3.4", "morgan": "^1.10.0", "multer": "^1.4.5-lts.1", "multer-s3": "2.10.0", "nodejs-file-downloader": "^4.10.6", "posthog-node": "^4.2.0", "redis": "^4.6.5", "sharp": "^0.33.5", "socket.io": "^4.6.1", "source-map-support": "^0.5.21", "uuid": "^8.3.2", "winston": "^3.3.3", "winston-daily-rotate-file": "^4.5.5", "winston-transport": "^4.5.0", "xlsx": "^0.18.5", "xlsx-populate": "^1.21.0"}, "devDependencies": {}}