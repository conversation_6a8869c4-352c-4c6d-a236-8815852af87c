import mongoose from 'mongoose';
import config from '@config';
import models from '@models';

async function connectToDatabase() {
    try {
        await mongoose.connect(
            'mongodb+srv://dbUserProd:<EMAIL>/EIR-PROD?retryWrites=true&w=majority',
            {
                serverSelectionTimeoutMS: 30000, // Increase timeout to 30 seconds
            },
        );
        console.log('Connected to MongoDB');
    } catch (error) {
        console.error('Error connecting to MongoDB:', error);
        process.exit(1);
    }
}

const entityCompanies = [
    {
        name: 'ABC Pte Ltd',
        uen: '52994950D',
        address:
            '226A Ang Mo Kio Avenue 1, #01-633, Kebun Baru Mall, Singapore 561226',
        unit: '#01-633',
        street_name: 'Ang Mo Kio Avenue 1',
        postal_code: '561226',
        building_name: 'Kebun Baru Mall',
        block_house_number: '226A',
    },
    {
        name: 'DEF Pte Ltd',
        uen: '48277200W',
        address:
            '50 Serangoon North Avenue 4, #03-08, First Centre, Singapore 555856',
        unit: '#03-08',
        street_name: 'Serangoon North Avenue 4',
        postal_code: '555856',
        building_name: 'First Centre',
        block_house_number: '50',
    },
    {
        name: 'GHI Pte Ltd',
        uen: '201526205Z',
        address: '200 Cantonment Road, #15-00, Southpoint, Singapore 089763',
        unit: '#15-00',
        street_name: 'Cantonment Road',
        postal_code: '089763',
        building_name: 'Southpoint',
        block_house_number: '200',
    },
    {
        name: 'JKL Pte Ltd',
        uen: '200310645Z',
        address:
            '226A Ang Mo Kio Avenue 1, #01-633, Kebun Baru Mall, Singapore 561226',
        unit: '#01-633',
        street_name: 'Ang Mo Kio Avenue 1',
        postal_code: '561226',
        building_name: 'Kebun Baru Mall',
        block_house_number: '226A',
    },
    {
        name: 'MNO Pte Ltd',
        uen: '199102606M',
        address:
            '38 Defu Lane 10, #03-35, Defu Industrial Estate, Singapore 539215',
        unit: '#03-35',
        street_name: 'Defu Lane 10',
        postal_code: '539215',
        building_name: 'Defu Industrial Estate',
        block_house_number: '38',
    },
];

export async function seedEntity() {
    try {
        await connectToDatabase();

        const created_by = await models.User.findOne({
            user_name: 'admin',
        });

        if (!created_by) {
            throw new Error('Admin user not found');
        }

        await Promise.all(
            entityCompanies.map(async (entity) => {
                let entityCompany = await models.EntityCompany.findOne({
                    uen: entity.uen,
                });

                if (!entityCompany) {
                    entityCompany = await models.EntityCompany.create({
                        name: entity.name,
                        uen: entity.uen,
                        address: entity.address,
                        unit: entity.unit,
                        street_name: entity.street_name,
                        postal_code: entity.postal_code,
                        building_name: entity.building_name,
                        created_by: created_by._id,
                        block_house_number: entity.block_house_number,
                    });
                } else {
                    await models.EntityCompany.updateOne(
                        { _id: entityCompany._id },
                        {
                            address: entity.address,
                            unit: entity.unit,
                            street_name: entity.street_name,
                            postal_code: entity.postal_code,
                            building_name: entity.building_name,
                            updated_by: created_by._id,
                            block_house_number:
                                entity.block_house_number +
                                ' ' +
                                entity.street_name,
                        },
                    );
                }
            }),
        );

        console.log('Seeding completed successfully');
    } catch (error) {
        console.error('Error seeding entities:', error);
    } finally {
        await mongoose.disconnect();
    }
}
