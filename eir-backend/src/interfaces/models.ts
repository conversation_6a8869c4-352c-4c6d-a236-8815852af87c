import { Document, Schema } from 'mongoose';

export interface IAdmin extends Document {
    avatar: string;
    username: string;
    password: string;
    role: string;
    permission: string[];
    token: string;
    locked_at: Date;
    created_at: Date;
    updated_at: Date;
    login_attempts: number;
}

export interface IContent extends Document {
    lang: string;
    data: object;
    updated_at: Date;
}

export interface IUser extends Document {
    token: string;
    token_expire_at: Date;
    username: string;
    fullname: string;
    mobile: string;
    group: string;
    company: Schema.Types.ObjectId;
    email: string;
    role: string;
    roleAcl: Schema.Types.ObjectId;
    signature: string;
    nric: string;
    vehicle_number: string;
}

export interface IEntity extends Document {
    unique_id: string;
    uen: string;
    name: string;
    address: string;
    street_name: string;
    postal_code: string;
    block_house_number: string;
    building_name: string;
    unit: string;
    type: string;
    status: string;
    saved_emails: string[];
    created_by: Schema.Types.ObjectId;
    linked_by: Schema.Types.ObjectId;
    linked_to: Schema.Types.ObjectId;
    location: {
        type: string;
        coordinates: number[];
    };
    linked_receipts: Schema.Types.ObjectId[];
    created_by_company: Schema.Types.ObjectId;
    entity_company: Schema.Types.ObjectId;
    is_unknown_entity: boolean;
    deleted_at: Date;
}

export interface IJourney extends Document {
    container: Schema.Types.ObjectId;
    receipts: Schema.Types.ObjectId[];
    start_receipt: Schema.Types.ObjectId;
    end_receipt: Schema.Types.ObjectId;
    status: string;
    type: string;
}

export interface ISetting extends Document {
    damage_side: {
        left: string;
        right: string;
        top: string;
        bottom: string;
        front: string;
        back: string;
        rear: string;
        inside: string;
        outside: string;
    };
    damage_region: string[];
    damage_type: string[];
}

export interface IContainer extends Document {
    unique_id: string;
    container_number: string;
    container_type: string;
    container_size: string;
    container_weight: string;
    iso_code: string;
    size: string;
    company: string;
}

export interface IReceipt extends Document {
    date: Date;

    unique_id: string;
    container: Schema.Types.ObjectId;
    container_loading: string;
    damages: any[];
    photos: string[];
    type: string;

    driver: string | Schema.Types.ObjectId;
    driver_signature: string;
    driver_action: string;
    vehicle_number: string;

    entity: string | Schema.Types.ObjectId;
    endorser: {
        name: string;
        nric: string;
        owner_name?: string;
        uen?: string;
        signature?: string;
        label?: string;
        title?: string;
        data?: any;
    };
    surveyor: {
        name: string;
        nric: string;
        owner_name?: string;
        uen?: string;
        signature?: string;
        label?: string;
        title?: string;
        data?: any;
    };
    entity_signature: string;
    entity_action: string;
    entity_on_behalf: boolean;

    location?: {
        type: string;
        coordinates: number[];
    };
    created_by: string | Schema.Types.ObjectId;
    created_by_company: string | Schema.Types.ObjectId;
    facility_company?: string | Schema.Types.ObjectId;
    transporter_company?: string | Schema.Types.ObjectId;
    company: string | Schema.Types.ObjectId;

    journey: string | Schema.Types.ObjectId;
}

export interface IDamage extends Document {
    unique_id: string;
    date: Date;
    receipt: Schema.Types.ObjectId;
    container: Schema.Types.ObjectId;
    position: string;
    side: string;
    region: string;
    type: string;
    photos: string[];
    parent: Schema.Types.ObjectId;
    created_at: Date;
    created_by_company: Schema.Types.ObjectId;
}

export interface IGroup extends Document {
    name: string;
    code?: string;
    contactPerson?: {
        name?: string;
        phone?: string;
    };
    groupInfo?: {
        uen?: string;
        registrationDate?: Date;
        address?: string;
        postalCode?: string;
        phone?: string;
        email?: string;
    };
    status?: 'inactive' | 'active' | 'suspended' | 'terminated';
    parent?: Schema.Types.ObjectId;
    organizationType?: Schema.Types.ObjectId;
    logo?: string;
    timezone?: string;
    level?: number;
    copiedGroupInfo?: boolean;
    copiedContactPerson?: boolean;
    createdBy?: Schema.Types.ObjectId;
    updatedBy?: Schema.Types.ObjectId;
    trailerShareStatus?: boolean;
}

export interface IGroup extends Document {
    name: string;
    code?: string;
    contactPerson?: {
        name?: string;
        phone?: string;
    };
    groupInfo?: {
        uen?: string;
        registrationDate?: Date;
        address?: string;
        postalCode?: string;
        phone?: string;
        email?: string;
    };
    status?: 'inactive' | 'active' | 'suspended' | 'terminated';
    parent?: Schema.Types.ObjectId;
    organizationType?: Schema.Types.ObjectId;
    logo?: string;
    timezone?: string;
    level?: number;
    copiedGroupInfo?: boolean;
    copiedContactPerson?: boolean;
    createdBy?: Schema.Types.ObjectId;
    updatedBy?: Schema.Types.ObjectId;
    trailerShareStatus?: boolean;
}

export interface IModule {
    name: string;
    allowCreate: boolean;
    allowRead: boolean;
    allowUpdate: boolean;
    allowDelete: boolean;
}

export interface IRoleAcl extends Document {
    name: string;
    forCdas: boolean;
    modules: IModule[];
    createdBy: Schema.Types.ObjectId;
    updatedBy: Schema.Types.ObjectId;
}

export interface ILicense extends Document {
    name: string;
    status: boolean;
    tag: boolean;
}
export interface ISubscription extends Document {
    company: Schema.Types.ObjectId;
    license: Schema.Types.ObjectId;
    limit_container: number;
    end_date: Date;
    allowed_users: Schema.Types.ObjectId[]; // List of users allowed to access the EIR
    status: 'active' | 'inactive' | 'expired'; // License status
    created_by: Schema.Types.ObjectId;
    updated_by: Schema.Types.ObjectId;
    company_sub_updated_at: Date;
    company_sub_updated_by: Schema.Types.ObjectId;
}

export interface IEntityCompany extends Document {
    name: string;
    uen: string;
    address: string;
    postal_code: string;
    unit: string;
    block_house_number: string;
    bizfile: any;
    street_name: string;
    building_name: string;
    created_by: Schema.Types.ObjectId;
    updated_by: Schema.Types.ObjectId;
}

export interface IPhoto extends Document {
    key: string;
    location: {
        type: string;
        coordinates: number[];
    };
    street_name: string;
    block: string;
    building_name: string;
    portal_code: string;
    receipt: Schema.Types.ObjectId;
    damage: Schema.Types.ObjectId;
    created_by: Schema.Types.ObjectId;
}
