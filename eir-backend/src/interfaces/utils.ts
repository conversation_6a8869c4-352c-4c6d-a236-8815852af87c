import { Document, LeanDocument, Types } from 'mongoose';

/**
 * Convert union type to intersection type. [Doc](https://www.typescriptlang.org/docs/handbook/release-notes/typescript-2-8.html#type-inference-in-conditional-types)
 */
export type UnionToIntersection<U> = (U extends unknown ? (a: U) => void : never) extends (k: infer I) => void
    ? I
    : never;
export type Populated<T, U> = U extends Record<string, unknown>
    ? T extends (infer W)[]
        ? { [P in keyof W]: P extends keyof U ? U[P] : W[P] }[]
        : { [P in keyof T]: P extends keyof U ? U[P] : T[P] }
    : T;
export type ConstValue<T> = T extends Record<string | number | symbol, infer U> ? U : never;
export type DataType<T> = Omit<T, keyof Document> & { _id?: Types.ObjectId };
export type OneOrMany<T> = T | Array<T>;
export type OmitArray<T> = T extends Array<unknown> ? never : T;
export type NormalizeParam<T> = OneOrMany<T> | OneOrMany<Document<T & unknown>> | OneOrMany<LeanDocument<T & unknown>>;
