import { NextFunction, Request, Response, Router } from 'express';
import { AppResponse, AppRequest } from '@utils/baseResponse';

export interface ExpressMiddleware {
    (req: Request & AppRequest, res: Response & AppResponse, next: NextFunction): void;
}

export interface Route {
    method: string;
    path: string;
    cb: ExpressMiddleware;
    middleware: ExpressMiddleware[];
}

export interface Routes {
    authRoute: Route[];
    publicRoute: Route[];
}

export interface IRouteConfig {
    path?: string;
    router: Router;
}
