import SOCKET_EVENT from '@src/constants/socket';
import { Socket } from 'socket.io';
import { ExtendedError } from 'socket.io/dist/namespace';

export type SocketMiddleware = (
    req: { body: any; socket: Socket; [k: string]: any },
    res: SocketResponse,
    next: (err?: ExtendedError, errData?: any) => void,
) => any;
export type SocketHandler = (req: { body: any; socket: Socket; [k: string]: any }, res: SocketResponse) => any;
export type SocketResponse = {
    ok: (data?: any) => void;
    badRequest: (error_message?: string, data?: any) => void;
    rateLimited: (error_message?: string, data?: any) => void;
    serverInternalError: (error_message?: string, data?: any) => void;
};
export type SocketEvent = {
    event: keyof typeof SOCKET_EVENT;
    middlewares?: SocketMiddleware[];
    handler: So<PERSON><PERSON><PERSON><PERSON>;
};
