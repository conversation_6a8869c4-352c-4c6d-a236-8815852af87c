import { ConstValue } from './utils';

export const LEADERBOARD_DIRECTION = {
    LARGER_IS_BETTER: 'larger_is_better',
    SMALLER_IS_BETTER: 'smaller_is_better',
} as const;

export const UPDATE_SCORE_STRATEGY = {
    ACCUMULATE: 'accumulate',
    REPLACE_BETTER: 'replace_better',
} as const;

export type LeaderboardConfig = {
    leaderboardId?: string;
    updateScoreStrategy: ConstValue<typeof UPDATE_SCORE_STRATEGY>;
    getLeaderboardOrder?: (date?: Date | string | number) => string | number;
    direction: ConstValue<typeof LEADERBOARD_DIRECTION>;
};
