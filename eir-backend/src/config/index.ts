import fs from 'fs';
import path from 'path';
import { config } from 'dotenv';
import * as process from 'process';

config({ path: `.env.${process.env.NODE_ENV || 'development'}` });
console.log('env', process.env.NODE_ENV);

const configs = {
    session: {
        secret: process.env.SESSION_SECRET,
        name: process.env.APP_NAME || 'EIR',
    },
    encryption: {
        secret: process.env.ENCRYPT_SECRET,
        iv: process.env.ENCRYPT_IV,
    },
    keys: {
        admin: {
            private_key: fs.readFileSync(
                path.join(__dirname, '..', 'keys/admin/rsa.private'),
            ),
            public_key: fs.readFileSync(
                path.join(__dirname, '..', 'keys/admin/rsa.public'),
            ),
        },
        user: {
            private_key: fs.readFileSync(
                path.join(__dirname, '..', 'keys/user/rsa.private'),
            ),
            public_key: fs.readFileSync(
                path.join(__dirname, '..', 'keys/user/rsa.public'),
            ),
        },
    },
    app: {
        secretKey: process.env.SECRET_KEY,
        env: process.env.NODE_ENV || 'development',
        port: +process.env.PORT || 3000,
        isProd: process.env.NODE_ENV === 'production',
        isPentest: process.env.NODE_ENV === 'pentest',
        isDev: ['dev', 'development'].includes(process.env.NODE_ENV),
        name: process.env.APP_NAME,
        ORIGIN: [
            'http://localhost:8080',
            'http://localhost:9000',
            'https://portal.eir.ftech.ltd',
            'https://eeir.cdaslink.sg',
            'https://eeir-uat.cdaslink.sg',
            //Add all other domains here
        ],
    },
    amqpConnection: process.env.AMQP_URL,
    database: {
        uri:
            process.env.DATABASE_URI ||
            'mongodb://localhost:27017/dev?authSource=admin',
        options: {
            autoIndex: true,
            autoCreate: true,
        },
    },
    redis: {
        url: `redis://${process.env.REDIS_HOST}:${process.env.REDIS_PORT}`,
        password: process.env.REDIS_PASSWORD || '',
        database:
            (process.env.REDIS_DATABASE &&
                Number(process.env.REDIS_DATABASE)) ||
            0,
    },
    secret: {
        admin: process.env.ADMIN_SECRET,
        merchant: process.env.MERCHANT_SECRET,
        user: process.env.JWT_SECRET,
    },
    tokenExpiresTime: process.env.TOKEN_EXPIRES_TIME || '30d',
    timezone: process.env.TIMEZONE || 'Asia/Singapore',
    maxLoginAttempts: +process.env.MAX_LOGIN_ATTEMPTS || 5,
    maxOTPRequest: +process.env.MAX_OTP_REQUEST || 5,
    clients: {
        ctr_uat: {
            config: {
                baseURL: process.env.CTR_UAT_BASE_URL,
                headers: {
                    'Content-Type': 'application/json',
                },
            },
        },
        ctr: {
            config: {
                baseURL: process.env.CTR_BASE_URL,
                headers: {
                    'Content-Type': 'application/json',
                },
            },
        },
        roadname: {
            config: {
                baseURL: process.env.ROADNAME_BASE_URL,
                headers: {
                    'Content-Type': 'application/json',
                },
            },
        },
        entity: {
            config: {
                baseURL: process.env.ENTITY_BASE_URL,
                headers: {
                    'Content-Type': 'application/json',
                },
            },
        },
        onemap: {
            config: {
                baseURL: process.env.ONEMAP_BASE_URL,
                headers: {
                    'Content-Type': 'application/json',
                },
            },
        },
    },
};

export default configs;
