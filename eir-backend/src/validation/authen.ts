import { ROLE, SINGAPORE_MOBILE_NUMBER_REGEX } from '@src/constants/app';
import { validatePassword } from '@utils/password';
import { check } from 'express-validator';

export const email = check('email').isEmail().normalizeEmail({
    gmail_remove_dots: false,
});

export const mobile_number = check('mobile_number')
    .custom((value, { req }) => {
        req.body.mobile_number = value = value?.replace(/\s/g, '').replace('+', '');
        if (!SINGAPORE_MOBILE_NUMBER_REGEX.test(value)) throw new Error('Invalid Mobile Number');
        return true;
    })
    .withMessage('invalid_mobile_number')
    .customSanitizer((v) => v?.replace(/\s/g, '').replace('+', ''));

export const password = check('password').isString();

export const strongPassword = (field) =>
    check(field)
        .isStrongPassword({
            minLength: 8,
            minLowercase: 1,
            minUppercase: 1,
            minNumbers: 1,
            minSymbols: 1,
        })
        .withMessage('password_not_meet_requirement');
export const passwordMatch = check('password')
    .isString()
    .notEmpty()
    .custom((password) => {
        const invalid = validatePassword(password);
        if (invalid) throw new Error(invalid);
        return true;
    });

export const role = check('role').isIn(Object.values(ROLE));
export const required_secret_questions = check('answer').isString();
