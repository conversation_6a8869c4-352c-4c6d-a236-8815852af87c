import { check } from 'express-validator';

export const string = (field: string, length = 255) => check(field).isString().isLength({ max: length });
export const boolean = (field: string) => check(field).isBoolean();
export const number = (field: string) => check(field).isNumeric();
export const float = (field: string) => check(field).isFloat();
export const array = (field: string, options) => check(field).isArray(options);
export const object = (field: string) => check(field).isObject();
export const objectId = (field: string) => check(field).isMongoId();
export const inArray = (field: string, values) => check(field).isIn(values);
export const ISODate = (field: string) => check(field).isISO8601();
export const alpha = (field: string, length) => check(field).isAlphanumeric().isLength({ max: length || 255 });

