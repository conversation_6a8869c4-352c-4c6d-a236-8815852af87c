import * as _ from 'lodash';

type AscendingOrder = 'asc' | 1 | '1';
type DescendingOrder = 'desc' | -1 | '-1';
type SortOrder = AscendingOrder | DescendingOrder | Array<any>;
type CompareOption = {
    parser?: ClassDecorator | ((...args: any) => any);
    sortOrder: SortOrder;
};
type NormalizedSortOption = Record<string | number | symbol, SortOrder | CompareOption>;
type SortOption = NormalizedSortOption | Array<string> | string;

function normalizeSortOption(options: SortOption): NormalizedSortOption {
    if (typeof options === 'string') return normalizeSortOption(options.split(' '));

    if (Array.isArray(options)) {
        const optionObj: NormalizedSortOption = {};

        for (const field of options) {
            const sortOrder: SortOrder = field.startsWith('+') ? 'asc' : field.startsWith('-') ? 'desc' : 'asc';
            optionObj[field.replace(/^[\+\-]/, '')] = sortOrder;
        }

        return normalizeSortOption(optionObj);
    }

    return options;
}

function isCompareOption(option: CompareOption | SortOrder): option is CompareOption {
    return (option as CompareOption).sortOrder !== undefined;
}

function isAscending(sortOrder: SortOrder): sortOrder is AscendingOrder {
    return !Array.isArray(sortOrder) && ['asc', '1', 1].includes(sortOrder);
}

function compare(val1, val2, field, option: CompareOption | SortOrder): number {
    const v1 = _.get(val1, field);
    const v2 = _.get(val2, field);

    if (isCompareOption(option)) {
        if (option.parser) {
            _.set(val1, field, option.parser(v1));
            _.set(val2, field, option.parser(v2));
        }
        return compare(val1, val2, field, option.sortOrder);
    }

    if (Array.isArray(option)) {
        const index1 = option.indexOf(v1);
        const index2 = option.indexOf(v2);
        const dir = index1 - index2;
        return dir;
    }

    const sign = isAscending(option) ? 1 : -1;
    const dir = v1 > v2 ? 1 : v1 === v2 ? 0 : -1;
    return dir * sign;
}

function advancedSort(array: Array<Record<string | number | symbol, any>>, ...options: SortOption[]) {
    const _options: NormalizedSortOption = _.merge({}, ...options.map((option) => normalizeSortOption(option)));
    const optionEntries = Object.entries(_options);

    array.sort((val1, val2) => {
        for (let i = 0; i < optionEntries.length; i++) {
            const [field, compareOption] = optionEntries[i];
            const dir = compare(val1, val2, field, compareOption);
            if (dir === 0) continue;
            return dir;
        }
        return 0;
    });

    return array;
}

export default advancedSort;
