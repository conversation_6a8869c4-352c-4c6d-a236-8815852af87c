import App from '@src/app';
import initService from '@src/bootstrap/initService';
import configs from '@src/config';
import initRouter from '@src/routes/mobile';
import portalRouter from '@src/routes/portal';
import validateEnv from '@utils/validateEnv';

validateEnv();
async function main() {
    const servicesConfig = configs.app.isDev
        ? {
              credentials: true,
              rabbitmq: false,
          }
        : {
              credentials: true,
              rabbitmq: false,
              redisOptions: { expireEvent: true },
          };

    console.log('______________________________');
    console.log(JSON.stringify(servicesConfig, null, 4));
    console.log('______________________________');

    await initService(servicesConfig);
    

    const app = new App([
        { path: '/api/portal', router: portalRouter() },
        { path: '/api', router: initRouter() },
    ]);
    app.listen();
}

main();
