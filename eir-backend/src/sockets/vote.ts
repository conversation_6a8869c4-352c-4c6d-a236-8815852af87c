import { SocketEvent } from '@src/interfaces/socket';
import { validateRequest } from '@src/middlewares';
import * as handlers from './handlers/vote';
import * as commonValidation from '@src/validation/common';

const events: SocketEvent[] = [
    {
        // @ts-ignore
        event: 'join_vote',
        middlewares: [commonValidation.objectId('vote_id').notEmpty(), validateRequest],
        handler: handlers.joinVote,
    },
];

export default events;
