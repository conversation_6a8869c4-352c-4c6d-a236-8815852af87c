import { SocketEvent } from '@src/interfaces/socket';
import { validateRequest } from '@src/middlewares';
import { check } from 'express-validator';
import * as handlers from './handlers/test';

const events: SocketEvent[] = [
    {
        // @ts-ignore
        event: 'huhu',
        middlewares: [check('number').isInt().notEmpty(), validateRequest],
        handler: handlers.test,
    },
];

export default events;
