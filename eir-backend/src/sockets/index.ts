import { SocketEvent } from '@src/interfaces/socket';
import models from '@src/models';
import fs from 'fs';
import { Socket } from 'socket.io';

export async function onSocketConnect(socket: Socket) {
    console.log('User connected', socket.data.user_id, socket.id);
}

export async function onSocketDisconnect(socket: Socket) {
    console.log('User disconnected', socket.data.user_id, socket.id);
    const now = Date.now();
}

export default function initSocketEvents() {
    const events: SocketEvent[] = [];
    const dir = fs.readdirSync(__dirname).filter((f) => !f.includes('index.js') && f.endsWith('.js'));
    for (const file of dir) {
        const e = require(__dirname + '/' + file);
        events.push(...e.default);
    }
    return events;
}
