import configs from '@src/config';
import { getRedisClient } from '@src/utils/redis';

export interface TimeoutConfig {
    type: string;
    callback: Function;
    timeout: number;
}

type SetTimeoutParams = {
    identifiers: string[];
    value?: string;
    timeout?: number;
};
type ClearTimeoutParams = { identifiers?: string[]; key?: string };

class Timeout {
    private type: string;
    private callback: Function;
    private timeout: number;

    constructor({ type, callback, timeout }: TimeoutConfig) {
        this.type = type;
        this.callback = callback;
        this.timeout = timeout;
    }

    private genKey(...args: string[]) {
        return `${configs.app.name}_TIMEOUT:${this.type}|${args.join('__')}`;
    }

    public async set({ identifiers = [], value, timeout }: SetTimeoutParams): Promise<string> {
        const key = this.genKey(...identifiers);
        await getRedisClient().client.set(key, value || this.genKey(...identifiers), {
            PX: timeout || this.timeout,
        });
        return key;
    }

    public async clear({ identifiers, key }: ClearTimeoutParams): Promise<void> {
        if (!identifiers?.length && !key) return;
        key = key || this.genKey(...identifiers);
        await getRedisClient().client.del(key);
    }

    public trigger(params) {
        this.callback(params);
    }
}

export default class TimeoutManager {
    private static instance: TimeoutManager;
    private timeouts: Record<string, Timeout> = {};

    private constructor() {}

    static getInstance() {
        if (!TimeoutManager.instance) {
            TimeoutManager.instance = new TimeoutManager();
        }

        return TimeoutManager.instance;
    }

    static setupTimeouts(configs: Array<TimeoutConfig>) {
        const instance = this.getInstance();
        for (const config of configs) {
            instance.timeouts[config.type] = new Timeout(config);
        }
    }

    static async onRedisKeyExpired(key: string) {
        try {
            const isValidKey = key.startsWith(`${configs.app.name}_TIMEOUT:`);
            if (!isValidKey) return;
            const type = key.split('|')[0].split(':')[1];
            if (!type) return;
            const identifiers = key.split('|')[1]?.split('__') || [];
            const instance = this.getInstance();
            instance.timeouts[type]?.trigger(identifiers);
    } catch (error) {
            console.log(error);
        }
    }

    static async setTimeout({
        type,
        identifiers = [],
        value,
        timeout,
    }: SetTimeoutParams & {
        type: string;
    }): Promise<string> {
        const instance = this.getInstance();
        return await instance.timeouts[type]?.set({ identifiers, value, timeout });
    }

    static async clearTimeout({
        type,
        identifiers,
        key,
    }: ClearTimeoutParams & {
        type: string;
    }): Promise<void> {
        const instance = this.getInstance();
        await instance.timeouts[type].clear({ identifiers, key });
    }
}
