import {
    getUser,
    getGroup,
    getRoleAcl,
} from '@src/routes/portal/service/ctr.service';
import Cron from './Cron';

export function startCron() {
    new Cron({
        key: 'getGroup',
        callback: getGroup,
        cronTime: Cron.CRON_TIME.EVERY_5_MIN,
    });

    new Cron({
        key: 'getUser',
        callback: getUser,
        cronTime: Cron.CRON_TIME.DAILY,
    });

    new Cron({
        key: 'getRoleAcl',
        callback: getRoleAcl,
        cronTime: Cron.CRON_TIME.DAILY,
    });
}
