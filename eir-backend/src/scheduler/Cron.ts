import { getRedisClient } from '@utils/redis';
import { CronJob } from 'cron';

export default class Cron {
    public cronJob;

    constructor({ cronTime, callback, timeZone = 'Asia/Singapore', start = true, key }) {
        const onTick = async function () {
            try {
                var raceConditionKey = await getRedisClient().isScheduler(key, 60);
                if (!raceConditionKey) return;
                await callback();
            } catch (error) {
                console.error('CRON ERROR');
            } finally {
                if (raceConditionKey) await getRedisClient().client.del(raceConditionKey);
            }
        };

        this.cronJob = CronJob.from({
            cronTime,
            onTick,
            start,
            timeZone,
        });
    }

    static CRON_TIME = {
        EVERY_MIN: '0 * * * * *',
        EVERY_5_MIN: '0 */5 * * * *',
        HOURLY: '1 0 * * * *',
        DAILY: '1 0 0 * * *',
        WEEKLY: '1 0 0 * * 1',
    };
}
