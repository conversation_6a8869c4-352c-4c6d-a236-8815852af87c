import { matchedData } from 'express-validator';
import models from '@models';
import { uploadFileToS3FromStream } from '@utils/s3';
import { randomString } from '@src/utils/string';

export async function createDamage(req, res) {
    
        const {
            date,
            photos,
            region,
            position,
            side,
            container,
            parent,
            type,
        } = matchedData(req);
        const containerEntity = await models.Container.findOne({
            _id: container,
        });
        if (!containerEntity) {
            return res.badRequest(undefined, {
                message: 'Container not found',
            });
        }
        const unique_id = await randomString(20);
        const damage = await models.Damage.create({
            date,
            unique_id,
            photos,
            region,
            position,
            side,
            type,
            container,
            parent,
            created_by_company: req.user.company,
        });
        return res.ok(damage);

}

export async function uploadImageDamage(req, res) {
    
        if (!req.files || !req.files.length) {
            return res.badRequest(undefined, {
                message: 'No files uploaded',
            });
        }

        const images = await Promise.all(
            req.files.map(async (file) => {
                const key = `unmap-damages/${file.originalname}`;
                await uploadFileToS3FromStream({
                    mimetype: file.mimetype,
                    filestream: file.buffer,
                    key,
                    publicAcl: false,
                });

                return key;
            }),
        );

        return res.ok({ files: images });

}
