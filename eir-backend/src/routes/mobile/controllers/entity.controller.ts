import { ENTITY_STATUS } from '@src/constants/logic';
import models from '@src/models';
import { matchedData } from 'express-validator';

export async function createEntity(req, res) {
    
        const { name, type, location, address, uen } = matchedData(req);

        // find if entity already exists by unique_id
        if (uen) {
            const entityExists = await models.Entity.findOne({
                uen,
                status: ENTITY_STATUS.APPROVED,
            });

            if (entityExists) return res.ok(entityExists);
        }
        const createEntity: any = {
            name,
            type,
            address,
            uen,
            created_by: req.user.id,
            created_by_company: req.user.company,
        };

        if (location && location.latitude && location.longitude) {
            createEntity.location = {
                type: 'Point',
                coordinates: [location?.longitude, location?.latitude],
            };
        }

        const entity = await models.Entity.create(createEntity);
        return res.ok(entity);

}

export async function filterEntity(req, res) {
    
        const { search, limit = 10000, page = 1 } = matchedData(req);
        const query: any = {
            status: ENTITY_STATUS.APPROVED,
            deleted_at: { $exists: false },
            $or: [
                { created_by_company: req.user.company },
                { created_by_company: { $exists: false } },
            ],
        };

        if (search) {
            query.$or = [
                { name: { $regex: search, $options: 'i' } },
                { uen: { $regex: search, $options: 'i' } },
            ];
        }
        const skip = (page - 1) * limit;
        const entities = await models.Entity.find(query)
            .populate('entity_company', 'name uen')
            .limit(limit)
            .skip(skip)
            .sort({ created_at: -1 });
        const total = await models.Entity.countDocuments(query);
        return res.ok({ items: entities, total, page: +page, limit: +limit });

}
