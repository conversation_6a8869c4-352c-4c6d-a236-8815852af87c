import { CTRClient } from '@src/utils/api';
import models from '@models';
import { normalizeUserProfile } from '../../normalize';

export async function validateToken(req, res) {
    const whitelist = {
        uat: [
            '66d55f09b654e76d16b73965', // Joshiel Company 1
            '66d55f60b654e76d16b7399a', // Joshiel Company 2
            '6837ec7f27872508fa91cd7b', // Joshiel Company 3
            '6838197327872508fa9242ab', // MJN corp
            '6838148927872508fa923630', // JMM
            '62a9804a1818350929c46d50', // FUTURETECH PTE. LTD.
            '676249af148eb0686cff2d2a', // victor transport
            '64c1e7d337fada0899a73442', // A] YQ Test Transport Association
            '64dafa13a6ab98483c9f6bea', // B] YQ Test Transport Association
            '64dafa3ea6ab98483c9f6d60', // C] YQ Test Transport Association
            '61e631027aa21f0944b4cc58', // CWT INTEGRATED PTE. LTD
        ],
        prod: [
            '60cc070964687d093608f806', // CDAS transporter
            '6539d561697bb03f9b3724f7', // YQ Test Company
            '6539d6310f617d189c4016e6', // QY Test Company
            '686f791ebb589747be9789a9', // JM Test Company
            '686f7936a471144eedab478d', // MJ Test Company
        ],
    };
    try {
        const token = req.headers.authorization;

        if (!token) {
            return res.unauthorized();
        }

        const data = await CTRClient.verifyToken(token);
        if (!data) {
            return res.unauthorized();
        }

        const { user, accessToken } = data;
        if (user) {
            console.log('LOG-user', user);
            console.log('LOG-process.env.NODE_ENV', process.env.NODE_ENV);
            console.log('LOG-whitelist', whitelist[process.env.NODE_ENV]);
            if (!whitelist[process.env.NODE_ENV].includes(user.company)) {
                console.log('LOG-User not whitelisted', user.company);
                return res.unauthorized('User not whitelisted');
            }
            const existingUser = await models.User.findOne({
                username: user.username,
            });

            if (existingUser) {
                await models.User.updateOne(
                    { username: user.username },
                    {
                        token: accessToken,
                        ...user,
                    },
                );
            } else {
                await models.User.create({ ...user, token: accessToken });
            }

            const updatedUser = await models.User.findOne({
                username: user.username,
            });

            console.log('LOG-updatedUser', updatedUser);

            // Check license validation for the user
            if (updatedUser.company) {
                const subscription = await models.Subscription.findOne({
                    company: updatedUser.company,
                    status: 'active',
                }).populate('license');

                if (!subscription) {
                    return res.forbidden(
                        'No active license found for your company',
                    );
                }

                // Check if subscription is expired
                const currentDate = new Date();
                if (
                    subscription.end_date &&
                    subscription.end_date < currentDate
                ) {
                    // Update subscription status to expired
                    await models.Subscription.updateOne(
                        { _id: subscription._id },
                        { status: 'expired' },
                    );
                    console.log(
                        'LOG-Company license expired',
                        updatedUser.company,
                    );
                    return res.forbidden('Company license has expired');
                }

                // Check if user is in the allowed users list
                const isUserAllowed = subscription.allowed_users.some(
                    (allowedUserId) =>
                        allowedUserId.toString() === updatedUser._id.toString(),
                );

                if (!isUserAllowed) {
                    console.log(
                        'LOG-User not in allowed users list',
                        updatedUser._id,
                    );
                    return res.forbidden(
                        'User is not authorized to access EIR with current company license',
                    );
                }

                // Check if license is active
                // if (!subscription.license.status) {
                //     console.log(
                //         'LOG-License inactive',
                //         subscription.license._id,
                //     );
                //     return res.forbidden('License is currently inactive');
                // }

                console.log(
                    'LOG-License validation passed for user',
                    updatedUser._id,
                );
            }

            return res.ok(normalizeUserProfile(updatedUser));
        }

        return res.unauthorized();
    } catch (error) {
        console.error(error);
        return res.serverInternalError(error.message);
    }
}
