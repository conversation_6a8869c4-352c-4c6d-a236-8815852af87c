import { matchedData } from 'express-validator';
import models from '@models';
import { mapS3UrlReceipts } from '../services/receipt.service';
import { convertISOCode } from '../services/container.service';

export async function updateInfo(req, res) {
    const { unique_id, iso_code } = matchedData(req);

    const { container_size, container_type, container_weight } =
        convertISOCode(iso_code);
    await models.Container.updateOne(
        { unique_id },
        {
            iso_code,
            container_size,
            container_type,
            container_weight,
        },
        { upsert: true },
    );
    const updatedContainer = await models.Container.findOne({ unique_id });
    return res.ok(updatedContainer);
}

export async function filterContainer(req, res) {
    try {
        const { search, limit = 10, page = 1 } = req.query;
        const query: any = {};
        if (search) {
            query.$or = [
                { unique_id: { $regex: `^${search}`, $options: 'i' } },
                { iso_code: { $regex: `^${search}`, $options: 'i' } },
            ];
        }
        const containers = await models.Container.find(query)
            .limit(limit)
            .skip((page - 1) * limit)
            .lean();
        const total = await models.Container.countDocuments(query);
        await Promise.all(
            containers.map(async (container: any) => {
                container.last_receipt = await models.Receipt.findOne({
                    container: container._id,
                }).sort({ date: -1 });
                return container;
            }),
        );

        return res.ok({ items: containers, total, page: +page, limit: +limit });
    } catch (error) {
        return res.serverInternalError(error.message);
    }
}

export async function createContainer(req, res) {
    const {
        unique_id,
        container_number,
        iso_code,
        container_weight,
        container_size,
        container_type,
        company,
        entity,
    } = matchedData(req);
    const existContainer = await models.Container.findOne({
        unique_id,
        iso_code,
    });
    if (existContainer) return res.ok(existContainer);
    const container = await models.Container.create({
        unique_id,
        iso_code,
        container_number,
        container_weight,
        container_size,
        container_type,
        company,
        entity,
    });
    return res.ok(container);
}

export async function getContainer(req, res) {
    const { _id } = req.params;
    const container: any = await models.Container.findOne({ _id }).lean();
    const receipts = await models.Receipt.find({
        container: _id,
    })
        .limit(10)
        .populate(
            'damages',
            'unique_id position side region type photos created_at date',
        )
        .sort({ created_at: -1 })
        .lean();

    const receiptsWithUrl = await mapS3UrlReceipts(receipts);

    return res.ok({
        ...container,
        receipts: receiptsWithUrl,
    });
}
