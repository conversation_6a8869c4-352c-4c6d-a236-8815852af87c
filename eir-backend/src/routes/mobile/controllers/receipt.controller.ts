import { OneMapClient } from '@src/utils/api';
import { matchedData } from 'express-validator';
import models from '@models';
import {
    addReceiptId,
    generateUniqueID,
    updateDriverSignature,
    getRoadNameInReceipt,
    updateContainerJourney,
} from '../services/receipt.service';
import { getSignedUrlForS3, uploadFileToS3FromStream } from '@src/utils/s3';
import {
    DRIVER_ACTION,
    ENTITY_ACTION,
    ENTITY_STATUS,
} from '@src/constants/logic';

export async function filterReceipts(req, res) {
    const {
        search,
        container,
        limit = 10,
        page = 1,
        sign_on_behalf,
    } = req.query;

    // Add filter by created_by_company if user has company
    const created_by_company =
        req.user && req.user.company ? req.user.company : null;

    const query: any = {};

    // Filter by created_by_company if it exists
    if (created_by_company) {
        query.created_by_company = created_by_company;
    }

    if (search)
        query.$or = [
            {
                unique_id: { $regex: search, $options: 'i' },
            },
        ];
    if (container) query.container = container;
    const receipts = await models.Receipt.find(query)
        .limit(limit)
        .skip((page - 1) * limit)
        .sort({ date: -1 })
        .populate('container', 'unique_id iso_code size')
        .populate('company', 'name')
        .populate('driver', 'fullname email')
        .lean();
    await Promise.all(
        receipts.map(async (receipt) => await getRoadNameInReceipt(receipt)),
    );

    const total = await models.Receipt.countDocuments(query);
    return res.ok({ items: receipts, total, page: +page, limit: +limit });
}

// Helper function to determine role type and title based on driver action and participant
function getRoleInfo({ driver_action, isDriverRole, isSurveyor }) {
    if (driver_action === DRIVER_ACTION.DROP_OFF) {
        if (isDriverRole) {
            // Driver role in DROP_OFF
            return {
                label: 'Transporter Company',
                title: isSurveyor ? 'Taking Over' : 'Handing Over',
            };
        } else {
            // Entity role in DROP_OFF
            return {
                label: 'Facility Company',
                title: isSurveyor ? 'Taking Over' : 'Handing Over',
            };
        }
    } else if (driver_action === DRIVER_ACTION.PICK_UP) {
        if (isDriverRole) {
            // Driver role in PICK_UP
            return {
                label: 'Transporter Company',
                title: isSurveyor ? 'Taking Over' : 'Handing Over',
            };
        } else {
            // Entity role in PICK_UP
            return {
                label: 'Facility Company',
                title: isSurveyor ? 'Taking Over' : 'Handing Over',
            };
        }
    }
    return { label: null, title: null };
}

// Helper function to prepare data based on role type
function prepareRoleData({ isTransporter, company, foundEntity }) {
    console.log('prepareRoleData', { isTransporter, company, foundEntity });
    if (isTransporter) {
        return {
            owner_name: company?.name || '_',
            uen: company?.groupInfo?.uen || '_',
            data: company?.groupInfo
                ? JSON.parse(JSON.stringify(company.groupInfo))
                : {},
        };
    } else {
        return {
            owner_name:
                foundEntity?.entity_company?.name || foundEntity?.name || '_',
            uen: foundEntity?.entity_company?.uen || foundEntity?.uen || '_',
            data: foundEntity?.entity_company
                ? JSON.parse(JSON.stringify(foundEntity.entity_company))
                : {},
        };
    }
}

// Helper function to create surveyor object
function createSurveyor({
    user,
    signature,
    entityPic,
    entityOnBehalf,
    label,
    title,
    roleData,
}) {
    console.log('createSurveyor');

    return {
        name: entityOnBehalf
            ? 'Sign on behalf'
            : entityPic?.name || user?.fullname || '_',
        nric: entityOnBehalf
            ? 'Sign on behalf'
            : entityPic?.nric || user?.nric || '_',
        owner_name: roleData.owner_name,
        uen: roleData.uen,
        signature: signature,
        data: roleData.data,
        label: label,
        title: title,
    };
}

// Helper function to create endorser object
function createEndorser({
    user,
    signature,
    entityPic,
    entityOnBehalf,
    label,
    title,
    roleData,
}) {
    console.log('createEndorser');

    return {
        name: entityOnBehalf
            ? 'Sign on behalf'
            : entityPic?.name || user?.fullname || '_',
        nric: entityOnBehalf
            ? 'Sign on behalf'
            : entityPic?.nric || user?.nric || '_',
        owner_name: roleData.owner_name,
        uen: roleData.uen,
        signature: signature,
        data: roleData.data,
        label: label,
        title: title,
    };
}

// Helper function to assign surveyor and endorser based on driver action
function assignSurveyorAndEndorser({
    driver_action,
    foundDriver,
    foundEntity,
    req,
    entity_signature,
    entity_pic,
    entity_on_behalf,
    driver_signature,
}) {
    console.log('foundEntity', foundEntity);

    const assignments = {
        [DRIVER_ACTION.DROP_OFF]: {
            // Entity is surveyor (Facility), Driver is endorser (Transporter)
            surveyor: () => {
                const roleInfo = getRoleInfo({
                    driver_action,
                    isDriverRole: false,
                    isSurveyor: true,
                });
                const isTransporter = roleInfo.label === 'Transporter Company';
                const roleData = prepareRoleData({
                    isTransporter,
                    company: foundDriver.company,
                    foundEntity,
                });

                return createSurveyor({
                    user: foundEntity,
                    signature: entity_signature,
                    entityPic: entity_pic,
                    entityOnBehalf: entity_on_behalf,
                    label: roleInfo.label,
                    title: roleInfo.title,
                    roleData: roleData,
                });
            },
            endorser: () => {
                const roleInfo = getRoleInfo({
                    driver_action,
                    isDriverRole: true,
                    isSurveyor: false,
                });
                const isTransporter = roleInfo.label === 'Transporter Company';
                const roleData = prepareRoleData({
                    isTransporter,
                    company: foundDriver.company,
                    foundEntity: foundEntity,
                });

                return createEndorser({
                    user: req.user,
                    signature: driver_signature,
                    entityPic: null,
                    entityOnBehalf: false,
                    label: roleInfo.label,
                    title: roleInfo.title,
                    roleData: roleData,
                });
            },
        },
        [DRIVER_ACTION.PICK_UP]: {
            // Driver is surveyor (Transporter), Entity is endorser (Facility)
            surveyor: () => {
                const roleInfo = getRoleInfo({
                    driver_action,
                    isDriverRole: true,
                    isSurveyor: true,
                });
                const isTransporter = roleInfo.label === 'Transporter Company';
                const roleData = prepareRoleData({
                    isTransporter,
                    company: foundDriver.company,
                    foundEntity: foundEntity,
                });

                return createSurveyor({
                    user: req.user,
                    signature: driver_signature,
                    entityPic: null,
                    entityOnBehalf: false,
                    label: roleInfo.label,
                    title: roleInfo.title,
                    roleData: roleData,
                });
            },
            endorser: () => {
                const roleInfo = getRoleInfo({
                    driver_action,
                    isDriverRole: false,
                    isSurveyor: false,
                });
                const isTransporter = roleInfo.label === 'Transporter Company';
                const roleData = prepareRoleData({
                    isTransporter,
                    company: foundDriver.company,
                    foundEntity,
                });

                return createEndorser({
                    user: foundEntity,
                    signature: entity_signature,
                    entityPic: entity_pic,
                    entityOnBehalf: entity_on_behalf,
                    label: roleInfo.label,
                    title: roleInfo.title,
                    roleData: roleData,
                });
            },
        },
    };

    const assignment = assignments[driver_action];
    if (!assignment) {
        throw new Error(`Invalid driver action: ${driver_action}`);
    }

    return {
        surveyor: assignment.surveyor(),
        endorser: assignment.endorser(),
    };
}

export async function newReceipt(req, res) {
    const {
        date,
        container,
        container_loading,
        damages,
        photos,
        type,
        driver_signature,
        driver_action,
        entity_signature,
        vehicle_number,
        entity,
        entity_on_behalf,
        entity_pic,
        location,
    } = matchedData(req);
    const unique_id = await generateUniqueID();
    const entity_action =
        driver_action == DRIVER_ACTION.DROP_OFF
            ? ENTITY_ACTION.PICK_UP
            : ENTITY_ACTION.DROP_OFF;
    // Create new receipt

    const createEntity: any = {
        date,
        container,
        container_loading,
        type,
        driver_signature,
        driver_action,
        vehicle_number,
        damages,
        photos,

        entity,
        entity_on_behalf,
        entity_signature,
        entity_action,

        unique_id,
        driver: req.user.id,
        created_by: req.user.id,
        created_by_company: req.user.company,
    };

    let foundEntity: any = {
        name: '',
    };
    if (entity) {
        foundEntity = await models.Entity.findById(entity).populate(
            'entity_company',
        );
        const receipt = await models.Receipt.exists({
            entity,
        });
        if (foundEntity.status == ENTITY_STATUS.PENDING && receipt) {
            return res.badRequest('Entity is not approved');
        }
    } else {
        const newEntity = await models.Entity.create({});
        createEntity.entity = newEntity._id;
        foundEntity = newEntity;
    }

    const foundDriver: any = await models.User.findOne({
        _id: req.user.id,
    }).populate('company', 'name groupInfo');

    // Assign surveyor and endorser based on driver action
    const { surveyor, endorser } = assignSurveyorAndEndorser({
        driver_action,
        foundDriver,
        foundEntity,
        req,
        entity_signature,
        entity_pic,
        entity_on_behalf,
        driver_signature,
    });

    createEntity.surveyor = surveyor;
    createEntity.endorser = endorser;
    createEntity.facility_company = foundEntity?.entity_company
        ? foundEntity.entity_company._id
        : null;
    createEntity.transporter_company = foundDriver?.company
        ? foundDriver.company._id
        : null;

    if (location && location.latitude && location.longitude) {
        // fake location
        // location.latitude = 1.37547396856067;
        // location.longitude = 103.876177822619;
        const [roadName, onemap_data] = await OneMapClient.getRoadName(
            +location.latitude,
            +location.longitude,
            true, // true to get onemap_data
        );
        createEntity.location = {
            ...location,
            type: 'Point',
            coordinates: [location?.longitude, location?.latitude],
            address: roadName || 'Invalid SG address',
            onemap_data,
        };
    }

    console.log('LOG-createEntity', createEntity);

    const receipt = await (
        await models.Receipt.create(createEntity)
    ).populate('damages', 'photos _id unique_id');

    // Update damages with receipt id
    addReceiptId(receipt);
    // Update driver driver_signature
    updateDriverSignature(receipt);
    // Update Container Journey
    updateContainerJourney(receipt);

    return res.ok(receipt);
}

export async function uploadImageReceipt(req, res) {
    if (!req.files || !req.files.length) {
        return res.badRequest(undefined, {
            message: 'No files uploaded',
        });
    }

    const images = await Promise.all(
        req.files.map(async (file) => {
            const key = `unmap-receipts/${file.originalname}`;
            await uploadFileToS3FromStream({
                mimetype: file.mimetype,
                filestream: file.buffer,
                key,
                publicAcl: false,
            });

            return key;
        }),
    );

    return res.ok({ files: images });
}

export async function getReceipt(req, res) {
    const { id } = req.params;
    const receipt = await models.Receipt.findById(id)
        .populate('container')
        .populate('company', 'name')
        .populate('driver', 'fullname email')
        .populate('damages', '-receipt')
        .populate('endorser', 'name nric')
        .populate('entity')
        .lean();
    if (!receipt) {
        return res.notFound('Receipt not found');
    }

    receipt.driver_signature = await getSignedUrlForS3(
        receipt.driver_signature,
        3600,
    );

    receipt.damages = await Promise.all(
        receipt.damages.map(async (damage: any) => {
            damage.photos = await Promise.all(
                damage.photos.map(async (photo) => {
                    return await getSignedUrlForS3(photo, 3600);
                }),
            );
            return damage;
        }),
    );

    receipt.photos = await Promise.all(
        receipt.photos.map(async (photo) => {
            return await getSignedUrlForS3(photo, 3600);
        }),
    );

    return res.ok(receipt);
}
