import models from '@models';
import { normalizeUserProfile } from '@src/routes/normalize';
import { getSignedUrlForS3, uploadFileToS3FromStream } from '@src/utils/s3';

export async function profile(req, res) {
    const user: any = await models.User.findOne({
        username: req.user.username,
    });

    return res.ok(normalizeUserProfile({
        ...user.toObject(),
        signature_url: await getSignedUrlForS3(user.signature, 3600),
    }));
}

export async function updateProfile(req, res) {
    
        const { nric, vehicle_number, signature } = req.body;
        await models.User.updateOne(
            {
                username: req.user.username,
            },
            { nric, vehicle_number, signature },
        );

        const user: any = await models.User.findOne({
            username: req.user.username,
        });
        return res.ok(
            normalizeUserProfile({
                ...user.toObject(),
                signature_url: await getSignedUrlForS3(user.signature, 3600),
            }),
        );

}

export async function uploadSignature(req, res) {
    
        if (!req.files || !req.files.length) {
            return res.badRequest(undefined, {
                message: 'No files uploaded',
            });
        }

        const images = await Promise.all(
            req.files.map(async (file) => {
                const key = `user-signature/${req.user.username}/${file.originalname}`;
                await uploadFileToS3FromStream({
                    mimetype: file.mimetype,
                    filestream: file.buffer,
                    key,
                    publicAcl: false,
                });

                return key;
            }),
        );

        return res.ok({ signature: images[0] });

}
