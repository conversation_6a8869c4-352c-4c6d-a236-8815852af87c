import { getSignedUrlForS3, uploadFileToS3FromStream } from '@utils/s3';
import { models } from 'mongoose';
import sharp from 'sharp';
import { OneMapClient } from '@src/utils/api';
import { formatDateWithTimezone } from '@utils/datetime';

export async function uploadImage(req, res) {
    try {
        console.log('123', req.files);
        if (!req.files || !req.files.length) {
            return res.badRequest(undefined, {
                message: 'No files uploaded',
            });
        }

        const images = await Promise.all(
            req.files.map(async (file) => {
                const key = `unmap/${file.originalname}`;
                const [latitude, longitude, timestamps] =
                    file.originalname.split('_');

                const photoData: any = {
                    key,
                    timestamps: parseInt(timestamps),
                    created_by: req.user.id,
                };
                let filestream = file.buffer;
                if (
                    !isNaN(parseFloat(latitude)) &&
                    !isNaN(parseFloat(longitude)) &&
                    parseFloat(latitude) !== 0 &&
                    parseFloat(longitude) !== 0
                ) {
                    const [roadName, onemap_data] =
                        await OneMapClient.getRoadName(
                            parseFloat(latitude),
                            parseFloat(longitude),
                            true,
                        );
                    const drawText = roadName;
                    console.log('LOG-drawText', drawText);
                    // use gm to write the text to image
                    const drawBuffer = await addTextToImageBuffer(
                        file.buffer,
                        drawText,
                        formatDateWithTimezone(
                            new Date(parseInt(timestamps)),
                            undefined,
                            'YYYY-MM-DD HH:mm:ss',
                        ),
                    );
                    filestream = drawBuffer;
                    photoData.location = {
                        type: 'Point',
                        coordinates: [
                            parseFloat(longitude),
                            parseFloat(latitude),
                        ],
                    };
                }

                await uploadFileToS3FromStream({
                    mimetype: file.mimetype,
                    filestream,
                    key,
                    publicAcl: false,
                });

                await models.Photo.create(photoData);
                return key;
            }),
        );

        const files = await Promise.all(
            images.map(async (image) => {
                return {
                    url: await getSignedUrlForS3(image, 3600),
                    key: image,
                };
            }),
        );
        return res.ok({ files });
    } catch (error) {
        console.error(error);
        return res.serverInternalError(error.message);
    }
}

async function addTextToImageBuffer(
    imageBuffer: Buffer,
    address: string,
    time: string,
): Promise<any> {
    const imageMetadata = await sharp(imageBuffer).metadata();
    const { width, height } = calculateResizedDimensions(
        imageMetadata.width,
        imageMetadata.height,
    );
    const svgWidth = isVertical(imageMetadata.orientation) ? height : width;

    const imageResized = await sharp(imageBuffer).resize(width, height);
    const resizedBuffer = await imageResized
        .withMetadata({
            orientation: imageMetadata.orientation,
        })
        .toBuffer();

    const svg = `
        <svg width="${svgWidth}" height="100">
            <rect x="0" y="0" width="100%" height="100%" fill="rgba(255, 255, 255, 0.7)" />
            <text x="10" y="30" font-size="20" fill="black">${address}</text>
            <text x="10" y="60" font-size="20" fill="black">${time}</text>
        </svg>
    `;
    const image = await sharp(resizedBuffer);
    if (isVertical(imageMetadata.orientation)) {
        console.log('rotate');
        image.rotate();
    }

    return image
        .composite([
            {
                input: Buffer.from(svg),
                gravity: sharp.gravity.northwest,
            },
        ])
        .toBuffer();
}

function calculateResizedDimensions(width: number, height: number) {
    console.log('LOG-width, height', width, height);
    const minWidth = 1024;
    const minHeight = 1024;

    let newWidth = width;
    let newHeight = height;

    // If the image is smaller than the minimum dimensions, return the original dimensions
    if (width < minWidth || height < minHeight) {
        return { width, height };
    }

    const ratio = Math.min(width / minWidth, height / minHeight);
    console.log('LOG-ratio', ratio);
    newWidth = width / ratio;
    newHeight = height / ratio;

    return { width: Math.round(newWidth), height: Math.round(newHeight) };
}

function isVertical(orientation: number) {
    return [6, 8].includes(orientation);
}
