import { Route } from '@interfaces/routes';
import * as controllers from '@src/routes/mobile/controllers/user.controller';
import * as commonValidations from '@src/validation/common';
import { validateRequest } from '@src/middlewares';
import { uploadMemStorage } from '@src/utils/s3';

export const authRoute: Route[] = [
    {
        method: 'get',
        path: '/profile',
        cb: controllers.profile,
        middleware: [],
    },
    {
        method: 'patch',
        path: '/profile',
        cb: controllers.updateProfile,
        middleware: [
            commonValidations.string('nric').optional(),
            commonValidations.string('vehicle_number').optional(),
            commonValidations.string('signature').optional(),
            validateRequest,
        ],
    },
    {
        method: 'post',
        path: '/upload-signature',
        cb: controllers.uploadSignature,
        middleware: [uploadMemStorage.array('signature', 1)],
    },
];

export const publicRoute: Route[] = [];
