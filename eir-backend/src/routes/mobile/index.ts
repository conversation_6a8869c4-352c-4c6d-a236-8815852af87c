import { Router } from 'express';
import { Routes } from '@src/interfaces/routes';

import fs from 'fs';
import path from 'path';
import authenticate from '@src/middlewares/authenticate';
import authenticateWithLicense from '@src/middlewares/authenticateWithLicense';

const basename = path.basename(__filename);

const router = Router();

function getPath(fileName) {
    if (fileName === 'public.route.js') return '';
    return `/${fileName.replace('.route.js', '')}`;
}

function wrapAsync(fn) {
    return function (req, res, next) {
        fn(req, res, next).catch((error) => {
            console.log(`LOG-error in ${fn.name}`, error);
            return res.serverInternalError(error.message);
        });
    };
}

export default function initRouter() {
    fs.readdirSync(__dirname)
        .filter((file) => {
            return (
                file.indexOf('.') !== 0 &&
                file !== basename &&
                file.slice(-3) === '.js'
            );
        })
        .forEach(async (file) => {
            const { authRoute, publicRoute } = (await import(
                path.join(__dirname, file)
            )) as Routes;
            authRoute?.forEach((route) => {
                console.log(
                    `[${file}] - ${route.method} ${getPath(file) + route.path}`,
                );
                router[route.method](
                    getPath(file) + route.path,
                    [authenticateWithLicense, ...(route.middleware || [])],
                    wrapAsync(route.cb),
                );
            });
            publicRoute?.forEach((route) => {
                console.log(
                    `[${file}] - ${route.method} ${getPath(file) + route.path}`,
                );
                router[route.method](
                    getPath(file) + route.path,
                    route.middleware || [],
                    route.cb,
                );
            });
        });
    return router;
}
