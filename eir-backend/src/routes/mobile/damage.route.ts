import { Route } from '@interfaces/routes';
import * as controllers from '@src/routes/mobile/controllers/damage.controller';
import * as commonValidations from '@src/validation/common';
import { validateRequest } from '@src/middlewares';
import { uploadMemStorage } from '@utils/s3';

export const authRoute: Route[] = [
    {
        method: 'post',
        path: '/',
        cb: controllers.createDamage,
        middleware: [
            commonValidations.string('date'),
            commonValidations.string('container'),
            commonValidations.string('side'),
            commonValidations.string('position'),
            commonValidations.string('region'),
            commonValidations.string('type'),
            commonValidations.string('parent').optional(),
            commonValidations.array('photos', { required: true }),
            validateRequest,
        ],
    },
    {
        method: 'post',
        path: '/upload',
        cb: controllers.uploadImageDamage,
        middleware: [
            uploadMemStorage.array('docs', 5),
        ],
    },
];

export const publicRoute: Route[] = [];
