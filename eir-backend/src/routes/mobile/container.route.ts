import { Route } from '@interfaces/routes';
import * as controllers from '@src/routes/mobile/controllers/container.controller';
import * as commonValidations from '@src/validation/common';
import { validateRequest } from '@src/middlewares';

export const authRoute: Route[] = [
    {
        method: 'patch',
        path: '/',
        cb: controllers.updateInfo,
        middleware: [
            commonValidations.string('unique_id'),
            commonValidations.string('iso_code'),
            validateRequest,
        ],
    },
    {
        method: 'get',
        path: '/',
        cb: controllers.filterContainer,
        middleware: [
            commonValidations.string('search').optional(),
            commonValidations
                .number('limit')
                .optional()
                .custom((value) => value > 0),
            commonValidations
                .number('page')
                .optional()
                .custom((value) => value > 0),
            validateRequest,
        ],
    },
    {
        method: 'get',
        path: '/:_id',
        cb: controllers.getContainer,
        middleware: [commonValidations.string('_id'), validateRequest],
    },
    {
        method: 'post',
        path: '/',
        cb: controllers.createContainer,
        middleware: [
            commonValidations.string('unique_id'),
            commonValidations.string('iso_code'),
            commonValidations.string('container_number').optional(),
            commonValidations.string('container_type').optional(),
            commonValidations.string('container_size').optional(),
            commonValidations.string('container_weight').optional(),
            // commonValidations.string('company'),
            // commonValidations.string('entity'),
            validateRequest,
        ],
    },
];

export const publicRoute: Route[] = [];
