import { IDamage, IReceipt } from '@src/interfaces/models';
import models from '@src/models';
import { getRedisClient } from '@src/utils/redis';

export async function generateDamageID(damage: IDamage, receipt: IReceipt) {
    const side = damage.side === 'component' ? 'C' : '';
    const regionMatch = damage.region.match(/([A-Za-z]*)(\d+)/);
    const regionPrefix = regionMatch ? regionMatch[1] : '';
    const regionNumber = regionMatch ? regionMatch[2] : damage.region.toString();
    const region = regionPrefix + regionNumber.padStart(2, '0');
    const requestIdx = await getRedisClient().increaseKey(
        `${receipt.unique_id}-${side}${region}${damage.type}`,
    );
    if (await models.Damage.exists({ unique_id: requestIdx })) {
        return generateDamageID(damage, receipt);
    }

    return `${receipt.unique_id}-${side}${region}${damage.type}${requestIdx
        .toString()
        .padStart(2, '0')}`;
}
