import { getMoment } from '@utils/datetime';
import { getRedisClient } from '@utils/redis';
import models from '@models';
import { IDamage, IReceipt } from '@src/interfaces/models';
import { getSignedUrlForS3, moveFile } from '@src/utils/s3';
import { OneMapClient } from '@src/utils/api';
import {
    ENTITY_STATUS,
    ENTITY_TYPE,
    JOURNEY_STATUS,
    JOURNEY_TYPE,
} from '@src/constants/logic';
import { generateDamageID } from './damage.service';

export async function generateUniqueID(date: Date = new Date()) {
    const dateStr = getMoment(date).format('YYMMDD');
    const requestIdx = await getRedisClient().increaseKey(
        `RECEIPT_ID_${dateStr}`,
    );
    if (await models.Receipt.exists({ unique_id: requestIdx })) {
        return generateUniqueID(date);
    }
    return `EIR${dateStr}${requestIdx.toString().padStart(6, '0')}`;
}

export async function generateJourneyID() {}

export async function addReceiptId(receipt: IReceipt) {
    try {
        const damages = await models.Damage.find({
            _id: { $in: receipt.damages },
        });

        await Promise.all(
            damages.map(async (damage) => {
                const unique_id = await generateDamageID(damage, receipt);
                await models.Damage.updateOne(
                    { _id: damage._id },
                    {
                        unique_id,
                        receipt: receipt._id,
                    },
                );
            }),
        );

        // Fetch the updated receipt with populated damage data
        const updatedReceipt = await models.Receipt.findById(
            receipt._id,
        ).populate('damages');

        // Move s3 files to receipt folder
        await moveImagesToReceiptFolder(updatedReceipt);

        return updatedReceipt;
    } catch (error) {
        console.error('Error updating damages with receipt id', error);
    }
}

export async function updateDriverSignature(receipt: IReceipt) {
    await models.User.updateOne(
        { _id: receipt.driver },
        { driver_signature: receipt.driver_signature },
    );
}

const updateKeyPhoto = async ({
    photo,
    newKey,
    receipt = null,
    damage = null,
}) => {
    await models.Photo.updateOne(
        {
            key: photo,
        },
        {
            key: newKey,
            receipt: receipt ? receipt._id : null,
            damage: damage ? damage._id : null,
        },
    );
};

export async function moveImagesToReceiptFolder(receipt: IReceipt) {
    try {
        const receiptPath = `receipts/${receipt.unique_id}`;

        receipt.photos = await Promise.all(
            receipt.photos.map(async (photo) => {
                const newKey = `${receiptPath}/${photo.replace('unmap/', '')}`;
                await moveFile(photo, newKey);
                // update photo key in photo model
                await updateKeyPhoto({ photo, newKey, receipt });
                return newKey;
            }),
        );

        receipt.damages = await Promise.all(
            receipt.damages.map(async (damage) => {
                damage.photos = await Promise.all(
                    damage.photos.map(async (photo, index) => {
                        // Extract the file extension from the original filename
                        const fileExtension = photo.split('.').pop();
                        console.log('damage', damage);
                        // Create new filename with the pattern damage.unique_id_Photo{index+1}
                        const newFileName = `${damage.unique_id}_Photo${
                            index + 1
                        }.${fileExtension}`;
                        const newKey = `${receiptPath}/damages/${damage._id}/${newFileName}`;
                        await moveFile(photo, newKey);
                        // update photo key in damage
                        await updateKeyPhoto({
                            photo,
                            newKey,
                            receipt,
                            damage,
                        });
                        return newKey;
                    }),
                );
                await models.Damage.updateOne(
                    {
                        _id: damage._id,
                    },
                    {
                        photos: damage.photos,
                    },
                );
                return damage;
            }),
        );

        if (receipt.driver_signature) {
            const newKey = `${receiptPath}/driver_signature/${receipt.driver_signature.replace(
                'unmap-receipts/',
                '',
            )}`;
            await moveFile(receipt.driver_signature, newKey);
            await updateKeyPhoto({ photo: receipt.driver_signature, newKey });
            receipt.driver_signature = newKey;
        }

        if (receipt.entity_signature) {
            const newKey = `${receiptPath}/entity_signature/${receipt.entity_signature.replace(
                'unmap-receipts/',
                '',
            )}`;
            await moveFile(receipt.entity_signature, newKey);
            await updateKeyPhoto({ photo: receipt.entity_signature, newKey });
            receipt.entity_signature = newKey;
        }

        await receipt.save();

        // save photo to photo model
        // await Promise.all(
        //     receipt.photos.map(async (photo) => {
        //         await models.Photo.create({
        //             key: photo,
        //             receipt: receipt._id,
        //             created_by: receipt.created_by,
        //         });
        //     }),
        // );
    } catch (error) {
        console.error('Error moving images to receipt folder', error);
    }

    // console.log('LOG-receipt', receipt);

    return receipt;
}

export async function mapS3UrlReceipts(receipts) {
    // modify photos
    await Promise.all(
        receipts.map(async (receipt: any) => {
            if (!receipt.photos) return receipt;
            receipt.photos = await Promise.all(
                receipt.photos.map(async (photo: any) => {
                    const s3Link = await getSignedUrlForS3(photo, 3600);
                    return s3Link;
                }),
            );

            if (receipt.endorser && receipt.endorser.signature)
                receipt.endorser.signature = await getSignedUrlForS3(
                    receipt.endorser.signature,
                    3600,
                );

            if (receipt.surveyor && receipt.surveyor.signature)
                receipt.surveyor.signature = await getSignedUrlForS3(
                    receipt.surveyor.signature,
                    3600,
                );
        }),
    );

    // modify damage object
    await Promise.all(
        receipts.map(async (receipt: IReceipt) => {
            const damages = await Promise.all(
                receipt.damages.map(async (damage: IDamage) => {
                    if (!damage.photos) return damage;

                    const photos_link = await Promise.all(
                        damage.photos.map(async (photo: string) => {
                            console.log(photo);
                            return await getSignedUrlForS3(photo, 3600);
                        }),
                    );

                    return {
                        ...damage,
                        photos_link, // Adding the new property
                    };
                }),
            );
            receipt.damages = damages;
            return receipt;
        }),
    );

    // modify signature
    await Promise.all(
        receipts.map(async (receipt: IReceipt) => {
            if (receipt.driver_signature) {
                receipt.driver_signature = await getSignedUrlForS3(
                    receipt.driver_signature,
                    3600,
                );
            }

            if (receipt.entity_signature) {
                receipt.entity_signature = await getSignedUrlForS3(
                    receipt.entity_signature,
                    3600,
                );
            }
        }),
    );

    return receipts;
}

export async function getRoadNameInReceipt(receipt) {
    try {
        const longitude = receipt.location.coordinates[0];
        const latitude = receipt.location.coordinates[1];
        if (!longitude || !latitude) return receipt;
        if (
            receipt.location.address &&
            receipt.location.address !== 'NOT_FOUND'
        )
            return receipt;

        const roadName = await OneMapClient.getRoadName(latitude, longitude);
        receipt.location.address = roadName;
        return receipt;
    } catch (error) {
        console.error('Error getting road name in receipt', error);
    }
}

export async function updateContainerJourney(receipt) {
    try {
        const journey = await models.Journey.findOne({
            container: receipt.container,
            status: JOURNEY_STATUS.ACTIVE,
        }).populate({
            path: 'start_receipt',
            populate: {
                path: 'entity',
            },
        });
        const entity = await models.Entity.findById(receipt.entity);
        if (!journey) {
            return notExistJourney({ entity, receipt });
        }
        return existJourney({ journey, entity, receipt });
    } catch (error) {
        console.error('Error updating container journey', error);
    }
}

async function notExistJourney({ entity, receipt }) {
    const createJourney: any = {
        receipts: [receipt._id],
        container: receipt.container,
        status: JOURNEY_STATUS.ACTIVE,
        type: receipt.type,
    };

    if ([ENTITY_TYPE.DEPOT, ENTITY_TYPE.PORT].includes(entity.type)) {
        createJourney.start_receipt = receipt._id;
    }
    await models.Journey.create(createJourney);
}

async function existJourney({ journey, entity, receipt }) {
    // handle case in another journey
    if (journey.type && receipt.type) {
        if (journey.type !== receipt.type) {
            await models.Journey.updateOne(
                { _id: journey._id },
                {
                    status: JOURNEY_STATUS.COMPLETED,
                },
            );
            // create new journey
            return notExistJourney({ entity, receipt });
        }
    }
    const updateJourney: any = {
        $push: { receipts: receipt._id },
    };
    if ([ENTITY_TYPE.DEPOT, ENTITY_TYPE.PORT].includes(entity.type)) {
        updateJourney.end_receipt = receipt._id;
        updateJourney.status = JOURNEY_STATUS.COMPLETED;
    }

    if (journey.start_receipt && updateJourney.end_receipt && !journey.type) {
        const startEntity = journey.start_receipt.entity;
        const endEntity = entity;
        updateJourney.type = getJourneyType(startEntity, endEntity);
    }

    await models.Journey.updateOne({ _id: journey._id }, updateJourney);
}

function getJourneyType(startEntity, endEntity) {
    if (startEntity.type === ENTITY_TYPE.DEPOT) {
        return endEntity.type === ENTITY_TYPE.PORT
            ? JOURNEY_TYPE.EXPORT
            : JOURNEY_TYPE.LOCAL;
    }

    if (startEntity.type === ENTITY_TYPE.PORT) {
        return endEntity.type === ENTITY_TYPE.DEPOT
            ? JOURNEY_TYPE.IMPORT
            : JOURNEY_TYPE.LOCAL;
    }

    return '';
}
