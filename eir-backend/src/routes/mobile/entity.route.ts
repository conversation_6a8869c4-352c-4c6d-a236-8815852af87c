import { Route } from '@interfaces/routes';
import * as controllers from '@src/routes/mobile/controllers/entity.controller';
import * as commonValidations from '@src/validation/common';
import { validateRequest } from '@src/middlewares';
import { uploadFile } from '../../middlewares/upload';

export const authRoute: Route[] = [
    {
        method: 'post',
        path: '/',
        cb: controllers.createEntity,
        middleware: [
            commonValidations.string('name'),
            commonValidations.string('type').optional(),
            commonValidations.object('location').optional(),
            commonValidations.string('address').optional(),
            commonValidations.string('uen').optional(),
            validateRequest,
        ],
    },
    {
        method: 'get',
        path: '/',
        cb: controllers.filterEntity,
        middleware: [
            commonValidations.string('search').optional(),
            validateRequest,
        ],
    },
    
];

export const publicRoute: Route[] = [];
