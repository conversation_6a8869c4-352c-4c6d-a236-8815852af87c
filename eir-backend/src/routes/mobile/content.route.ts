import models from '@models';
import { matchedData, check, body } from 'express-validator';
import { Route } from '@src/interfaces/routes';
import { validateHeadersMiddleware, validateRequest } from '@src/middlewares';

const LANG = {
    EN: 'en',
    ZH: 'zh',
};

export const publicRoute: Route[] = [
    {
        method: 'get',
        path: '/',
        cb: _getContent,
        middleware: [check('lang').isString().optional(), validateRequest],
    },
    {
        method: 'get',
        path: '/all',
        cb: _getAll,
        middleware: [check('lang').isString().optional(), validateRequest],
    },
    {
        method: 'put',
        path: '/up-to-date',
        cb: _updateContent,
        middleware: [validateHeadersMiddleware, body('data').notEmpty(), body('lang').isString().notEmpty(), validateRequest],
    },
];

export const authRoute: Route[] = [];

async function _getContent(req, res) {
    try {
        const { lang } = matchedData(req);
        console.log('lang', lang);
        let content = await models.Content.findOne({ lang });
        if (!content) return res.ok(await models.Content.findOne({ lang: LANG.EN }));

        return res.ok(content);
    } catch (error) {
        console.log('ERROR | Content _getContent', error);
        return res.serverInternalError(error.message);
    }
}

async function _getAll(req, res) {
    try {
        const { lang } = matchedData(req);
        const conditions: any = {};
        if (lang) conditions.lang = lang;
        let content = await models.Content.find(conditions);

        // return list content multi locale
        return res.ok(content);
    } catch (error) {
        console.log('ERROR | Content _getAll', error);
        return res.serverInternalError(error.message);
    }
}

async function _updateContent(req, res) {
    try {
        const { lang, data } = req.body;

        // update or create content
        await models.Content.updateOne({ lang }, { data }, { upsert: true });

        return res.ok(true);
    } catch (error) {
        console.log('ERROR | Content _updateContent', error);
        return res.serverInternalError(error.message);
    }
}
