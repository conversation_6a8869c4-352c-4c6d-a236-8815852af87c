import { Route } from '@interfaces/routes';
import * as controllers from '@src/routes/mobile/controllers/photo.controller';
import { uploadMemStorage } from '@src/utils/s3';

export const authRoute: Route[] = [
    {
        method: 'post',
        path: '/upload',
        cb: controllers.uploadImage,
        middleware: [uploadMemStorage.array('docs', 10)],
    },
];

export const publicRoute: Route[] = [];
