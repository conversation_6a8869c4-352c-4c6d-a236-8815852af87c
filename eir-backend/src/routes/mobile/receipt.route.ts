import { Route } from '@interfaces/routes';
import * as controllers from '@src/routes/mobile/controllers/receipt.controller';
import * as commonValidations from '@src/validation/common';
import { validateRequest } from '@src/middlewares';
import { uploadFile } from '../../middlewares/upload';
import { uploadMemStorage } from '@src/utils/s3';
import preventDuplicateSubmission from '@src/middlewares/preventDuplicateSubmission';

export const authRoute: Route[] = [
    {
        method: 'get',
        path: '/',
        cb: controllers.filterReceipts,
        middleware: [
            commonValidations.string('search').optional(),
            commonValidations
                .number('limit')
                .optional()
                .custom((value) => value > 0),
            commonValidations
                .number('page')
                .optional()
                .custom((value) => value > 0),
            validateRequest,
        ],
    },
    {
        method: 'post',
        path: '/',
        cb: controllers.newReceipt,
        middleware: [
            preventDuplicateSubmission,
            commonValidations.ISODate('date'),
            commonValidations.string('container'),
            commonValidations.string('container_loading'),
            commonValidations.array('damages', { required: false }),
            commonValidations.array('photos', { required: false }),
            commonValidations.string('type').optional(),
            commonValidations.string('driver_signature'),
            commonValidations.string('driver_action'),
            commonValidations.string('vehicle_number'),
            commonValidations.string('entity').optional(),
            commonValidations.string('entity_signature').optional(),
            commonValidations.boolean('entity_on_behalf'),
            commonValidations.object('entity_pic').optional(),
            commonValidations.object('location').optional(),
            validateRequest,
        ],
    },
    {
        method: 'post',
        path: '/upload',
        cb: controllers.uploadImageReceipt,
        middleware: [
            uploadMemStorage.array('docs', 10)
        ],
    },
    {
        method: 'get',
        path: '/:id',
        cb: controllers.getReceipt,
        middleware: [commonValidations.string('id'), validateRequest],
    },
];

export const publicRoute: Route[] = [];
