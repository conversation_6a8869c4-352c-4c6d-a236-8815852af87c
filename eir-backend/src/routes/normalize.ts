import { IUser } from '@interfaces/models';
import _ from 'lodash';

export function normalizeUserProfile(user: IUser) {
    if (user.company)
        return _.pick(user, [
            '_id',
            'username',
            'fullname',
            'email',
            'token',
            'mobile',
            'company',
            'nric',
            'vehicle_number',
            'signature',
            'signature_url',
        ]);

    return _.pick(user, [
        '_id',
        'username',
        'fullname',
        'email',
        'token',
        'mobile',
        'company',
        'nric',
        'roleAcl._id',
        'roleAcl.name',
    ]);
}
