import models from "@src/models";
import { CTRClient } from "@src/utils/api";

// write a function to get companies from CTR and save to DB
export async function getGroup() {
	try {
		console.log('-- Start getGroup --');
		const token = await CTRClient.getToken();
		const response: any = await CTRClient.client.get('/groups', {
            headers: {
                Authorization: `${token}`,
            },
        });

		const groups = response.data;
		// save to DB
		await models.Group.bulkWrite(
			groups.map(group => ({
				updateOne: {
					filter: { _id: group._id },
					update: { $set: group },
					upsert: true
				}
			}))
		);

		console.log('-- End getGroup --');

	} catch (error) {
		console.error('Error pulling companies', error);
	}
}