import { IReceipt } from '@src/interfaces/models';
import { getSignedUrlForS3 } from '@src/utils/s3';

export async function generateUniqueCodeEntity(type) {
    const code = type + Date.now();
    return code;
}

export async function mapUnlinkedSignatureURL(data: IReceipt[]) {
    // Convert S3 paths to presigned URLs for driver_signature and entity_signature
    await Promise.all(
        data.map(async (receipt: IReceipt) => {
            if (receipt.driver_signature) {
                receipt.driver_signature = await getSignedUrlForS3(
                    receipt.driver_signature,
                    3600,
                );
            }

            if (receipt.entity_signature) {
                receipt.entity_signature = await getSignedUrlForS3(
                    receipt.entity_signature,
                    3600,
                );
            }
        }),
    );

    return data;
}
