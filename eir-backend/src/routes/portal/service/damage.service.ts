import { IDamage } from '@src/interfaces/models';
import { getSignedUrlForS3 } from '@src/utils/s3';

export async function mapS3UrlDamages(damages) {
    return await Promise.all(
        damages.map(async (damage: IDamage) => {
            if (!damage.photos) return damage;

            const photos_link = await Promise.all(
                damage.photos.map(async (photo: string) => {
                    return await getSignedUrlForS3(photo, 3600);
                }),
            );

            return {
                ...damage,
                photos_link, // Adding the new property
            };
        }),
    );
}
