import models from '@src/models';
import { CTRClient, RoadNameClient } from '@src/utils/api';

// write a function to get companies from CTR and save to DB
export async function getUser() {
    try {
        console.log('-- Start getUser --');
        const token = await CTRClient.getToken();
        const response: any = await CTRClient.client.get('/users?$limit=1000', {
            headers: {
                Authorization: `${token}`,
            },
        });

        const users = response.data;
        
        // Prepare bulk operations instead of individual updates
        if (users && users.length > 0) {
            const bulkOps = users.map(user => ({
                updateOne: {
                    filter: { _id: user._id },
                    update: { $set: user },
                    upsert: true
                }
            }));
            
            // Execute all operations in a single database call
            await models.User.bulkWrite(bulkOps);
            console.log(`Updated ${users.length} users in bulk`);
        } else {
            console.log('No users to update');
        }
        
        console.log('-- End getUser --');
    } catch (error) {
        console.error('Error pulling users', error);
    }
}

export async function getGroup() {
    try {
        console.log('-- Start getGroup --');
        const token = await CTRClient.getToken();
        const response: any = await CTRClient.client.get(
            '/groups?$limit=1000',
            {
                headers: {
                    Authorization: `${token}`,
                },
            },
        );

        const groups = response.data;
        
        // Prepare bulk operations instead of individual updates
        if (groups && groups.length > 0) {
            const bulkOps = groups.map(group => ({
                updateOne: {
                    filter: { _id: group._id },
                    update: { $set: group },
                    upsert: true
                }
            }));
            
            // Execute all operations in a single database call
            await models.Group.bulkWrite(bulkOps);
            console.log(`Updated ${groups.length} groups in bulk`);
        } else {
            console.log('No groups to update');
        }
        
        console.log('-- End getGroup --');
    } catch (error) {
        console.error('Error pulling groups', error);
    }
}

export async function getRoleAcl() {
    try {
        console.log('-- Start getRoleAcl --');
        const token = await CTRClient.getToken();
        const response: any = await CTRClient.client.get(
            '/roles-acl?$limit=200',
            {
                headers: {
                    Authorization: `${token}`,
                },
            },
        );

        const roles = response.data;
        // save to DB
        for (const role of roles) {
            await models.RolesAcl.updateOne(
                { _id: role._id },
                { $set: role },
                { upsert: true },
            );
        }
        console.log('-- End getRoleAcl --');
    } catch (error) {
        console.error('Error pulling roleAcls', error);
    }
}

export async function createGroup(data) {
    try {
        const token = await CTRClient.getToken();
        const response: any = await CTRClient.client.post('/groups', data, {
            headers: {
                Authorization: `${token}`,
            },
        });

        const group = response.data;
        // save to DB
        await getGroup();
        return group;
    } catch (error) {
        console.error('Error creating group', error);
        throw error;
    }
}
