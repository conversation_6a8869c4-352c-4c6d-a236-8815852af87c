import { DRIVER_ACTION } from "@src/constants/logic";
import { IEntity, IReceipt } from "@src/interfaces/models";
import { models } from "mongoose";

export async function updateReceiptEntity(receipt: IReceipt, entity: IEntity) {
    const updateData = {
        entity: entity._id,
    };
    if (receipt.driver_action === DRIVER_ACTION.DROP_OFF) {
        updateData['surveyor.owner_name'] = entity?.name || '';
        updateData['surveyor.uen'] = entity?.uen || '';

    } else if (receipt.driver_action === DRIVER_ACTION.PICK_UP) {
        updateData['endorser.owner_name'] = entity?.name || '';
        updateData['endorser.uen'] = entity?.uen || '';
    }
    await models.Receipt.updateOne({ _id: receipt._id }, updateData);
}


export async function getReceiptFromCode(code: string) {
    const originalId = Buffer.from(code, 'base64').toString('ascii');
    const receipt = await models.Receipt.findOne({
        unique_id: originalId,
    })
        .populate('driver', 'fullname username nric')
        .populate('container', 'unique_id iso_code')
        .populate('created_by', 'username email fullname nric vehicle_number')
        .populate('damages')
        .populate('entity', 'name type uen saved_emails')
        .lean();
    return receipt;
}