import { EntityClient } from '@src/utils/api';
import { matchedData } from 'express-validator';
import { models } from 'mongoose';

export async function createCompanyEntity(req, res) {
    const {
        name,
        uen,
        address,
        location,
        postal_code,
        unit,
        street_name,
        block_house_number,
        building_name,
        bizfile,
    } = matchedData(req);
    const createData: any = {
        name,
        uen,
        postal_code,
        unit,
        street_name,
        block_house_number,
        building_name,
        bizfile,
        address,
        created_by: req.user.id,
    };

    if (location && location.latitude && location.longitude) {
        createData.location = {
            type: 'Point',
            coordinates: [location?.longitude, location?.latitude],
        };
    }

    const entity = await models.EntityCompany.create(createData);
    return res.ok(entity);
}

export async function getCompanyEntityByUen(req, res) {
    let { uen } = matchedData(req);
    
    // Trim the UEN to remove any whitespace
    uen = uen.trim();
    
    const entity = await models.EntityCompany.findOne({
        uen,
    });
    if (entity)
        return res.ok({
            entity_company: entity,
            bizfile: null,
        });

    const serverUen = await EntityClient.getDetail(uen);
    if (!serverUen) return res.notFound();

    return res.ok({
        entity_company: null,
        bizfile: serverUen,
    });
}
