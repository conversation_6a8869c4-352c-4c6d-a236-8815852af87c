import models from '@src/models';
import { mapS3UrlDamages } from '../service/damage.service';

export async function getDamages(req, res) {
    const {
        search = '',
        limit = 10,
        page = 1,
        sortBy = '_id',
        sort = 1,
    } = req.query;

    const query: any = {};
    if (search) {
        query.$or = [
            { unique_id: { $regex: search, $options: 'i' } },
            // { unique_id: search },
        ];
    }
    const damages = await models.Damage.find(query)
        .populate('receipt')
        .populate('container')
        .sort({ [sortBy]: sort }) // 1 for ascending, -1 for descending
        .limit(limit)
        .skip((page - 1) * limit)
        .lean();
    // const damagesWithUrl = await mapS3UrlDamages(damages);
    const total = await models.Damage.countDocuments(query);
    return res.ok({
        items: damages,
        total,
        page,
        limit,
    });
}

export async function getDamage(req, res) {
    const { _id } = req.params;
    const damage = await models.Damage.findById(_id)
        .populate('container')
        .populate('receipt')
        .lean();
    if (!damage) return res.notFound();
    const damageWithUrl = await mapS3UrlDamages([damage]);
    return res.ok(damageWithUrl[0]);
}

export async function downloadDamageReport(req, res) {
    const { ids = [], sortBy = '_id', sort = 1 } = req.query;

    const query: any = {};
    if (ids.length) {
        query._id = { $in: ids };
    }

    const damages = await models.Damage.find(query)
        .populate('receipt')
        .sort({ [sortBy]: sort }) // 1 for ascending, -1 for descending
        .lean();
}
