import { models } from 'mongoose';
import { getGroup } from '../service/ctr.service';

export async function listGroup(req, res) {
    await getGroup();
    const { search = '', level, limit = 10, page = 1 } = req.query;
    const query: any = {
        name: { $regex: search, $options: 'i' },
    };

    // Add filter by _id equal to the user's company
    if (req.user && req.user.company) {
        query._id = req.user.company;
    }

    if (level) query.level = level;
    const companies = await models.Group.find(query)
        .limit(limit)
        .skip((page - 1) * limit)
        .sort({ name: 1 })
        .populate('parent', 'name code logo')
        .lean();
    const total = await models.Group.countDocuments(query);
    return res.ok({ items: companies, total, page: +page, limit: +limit });
}
