import { ENTITY_STATUS } from '@src/constants/logic';
import { mapS3UrlReceipts } from '@src/routes/mobile/services/receipt.service';
import { getMoment } from '@src/utils/datetime';
import { matchedData } from 'express-validator';
import _ from 'lodash';
import { models } from 'mongoose';
import { updateReceiptEntity } from '../service/receipt.service';
import { mapUnlinkedSignatureURL } from '../service/entity.service';

export async function getListEntity(req, res) {
    const {
        search = '',
        status,
        limit = 10,
        page = 1,
        sortBy = 'created_at',
        sort = -1,
        name = '',
        type = '',
        address = '',
        geostamp = '',
        uen = '',
        entity_company = '',
        created_at = '',
        updated_at = '',
        created_by = '',
        container = '',
        vehicle = '',
        eir_id = '',
        driver_action = '',
        driver = '',
        linked_entity_type: linked_to_type = '',
        linked_entity_name: linked_to_name = '',
        linked_entity_company: linked_to_company = '',
        linked_entity_company_uen: linked_to_company_uen = '',
    } = req.query;

    // Get user's company if available
    const userCompany = req.user?.company || null;

    // Build the aggregation pipeline
    const pipeline = buildEntityPipeline({
        search,
        status,
        userCompany,
        name,
        type,
        address,
        geostamp,
        uen,
        entity_company,
        created_by,
        linked_to_type,
        linked_to_name,
        linked_to_company,
        linked_to_company_uen,
        driver_action,
        driver,
        container,
        vehicle,
        eir_id,
        created_at,
        updated_at,
        sortBy,
        sort,
        page,
        limit,
    });

    // Execute the aggregation pipeline
    const entities = await models.Entity.aggregate(pipeline);

    // Process results
    await enrichEntityResults(entities[0].data);

    // Return paginated response
    return res.ok({
        items: entities[0].data,
        total: entities[0].metadata[0]?.total || 0,
        page: +page,
        limit: +limit,
    });
}

/**
 * Builds the MongoDB aggregation pipeline for entity queries
 */
function buildEntityPipeline(params) {
    const {
        search,
        status,
        userCompany,
        name,
        type,
        address,
        geostamp,
        uen,
        entity_company,
        created_by,
        linked_to_type,
        linked_to_name,
        linked_to_company,
        linked_to_company_uen,
        driver_action,
        container,
        vehicle,
        eir_id,
        created_at,
        updated_at,
        sortBy,
        sort,
        page,
        limit,
    } = params;

    // Create lookup stages for related collections
    const lookupStages = buildLookupStages();

    // Create match stage with filters
    const matchStage = buildMatchStage(params);

    // Build complete pipeline
    const pipeline = [
        ...lookupStages, // Join related collections
        matchStage, // Apply filters
        buildConversionStage(), // Convert arrays to objects
        buildProjectionStage(), // Select fields to return
        { $sort: { [sortBy]: +sort } }, // Sort results
        buildPaginationStage(page, limit), // Paginate results
    ];

    return pipeline;
}

/**
 * Creates the lookup stages for joining related collections
 */
function buildLookupStages() {
    return [
        // Lookup for linked_to entities with their company data
        {
            $lookup: {
                from: 'entities',
                let: { linkedToId: '$linked_to' },
                pipeline: [
                    { $match: { $expr: { $eq: ['$_id', '$$linkedToId'] } } },
                    {
                        $lookup: {
                            from: 'entitycompanies',
                            localField: 'entity_company',
                            foreignField: '_id',
                            as: 'entity_company_data',
                        },
                    },
                    {
                        $addFields: {
                            entity_company: {
                                $arrayElemAt: ['$entity_company_data', 0],
                            },
                        },
                    },
                    { $project: { entity_company_data: 0 } },
                ],
                as: 'linked_to',
            },
        },
        // Lookup for linked receipts with container and driver data
        {
            $lookup: {
                from: 'receipts',
                let: { linkedReceiptIds: '$linked_receipts' },
                pipeline: [
                    {
                        $match: {
                            $expr: { $in: ['$_id', '$$linkedReceiptIds'] },
                        },
                    },
                    // Lookup container data
                    {
                        $lookup: {
                            from: 'containers',
                            localField: 'container',
                            foreignField: '_id',
                            as: 'container_data',
                        },
                    },
                    // Lookup driver data
                    {
                        $lookup: {
                            from: 'users',
                            localField: 'driver',
                            foreignField: '_id',
                            as: 'driver_data',
                        },
                    },
                    // Lookup entity data
                    {
                        $lookup: {
                            from: 'entities',
                            localField: 'entity',
                            foreignField: '_id',
                            as: 'entity_data',
                        },
                    },
                    // Convert arrays to single objects
                    {
                        $addFields: {
                            container: { $arrayElemAt: ['$container_data', 0] },
                            driver: { $arrayElemAt: ['$driver_data', 0] },
                            entity: { $arrayElemAt: ['$entity_data', 0] },
                        },
                    },
                    // Remove unused fields
                    {
                        $project: {
                            container_data: 0,
                            driver_data: 0,
                            entity_data: 0,
                            'driver.password': 0,
                            'driver.salt': 0,
                            'driver.token': 0,
                        },
                    },
                ],
                as: 'linked_receipts',
            },
        },
        // Lookup for unlinked receipts with container and driver data
        {
            $lookup: {
                from: 'receipts',
                let: { entityId: '$_id' },
                pipeline: [
                    {
                        $match: {
                            $expr: { $eq: ['$entity', '$$entityId'] },
                        },
                    },
                    // Lookup container data
                    {
                        $lookup: {
                            from: 'containers',
                            localField: 'container',
                            foreignField: '_id',
                            as: 'container_data',
                        },
                    },
                    // Lookup driver data
                    {
                        $lookup: {
                            from: 'users',
                            localField: 'driver',
                            foreignField: '_id',
                            as: 'driver_data',
                        },
                    },
                    // Lookup entity data
                    {
                        $lookup: {
                            from: 'entities',
                            localField: 'entity',
                            foreignField: '_id',
                            as: 'entity_data',
                        },
                    },
                    // Convert arrays to single objects
                    {
                        $addFields: {
                            container: { $arrayElemAt: ['$container_data', 0] },
                            driver: { $arrayElemAt: ['$driver_data', 0] },
                            entity: { $arrayElemAt: ['$entity_data', 0] },
                        },
                    },
                    // Remove unused fields
                    {
                        $project: {
                            container_data: 0,
                            driver_data: 0,
                            entity_data: 0,
                            'driver.password': 0,
                            'driver.salt': 0,
                            'driver.token': 0,
                        },
                    },
                ],
                as: 'unlinked_receipts',
            },
        },
        // Lookup for user who created the entity
        {
            $lookup: {
                from: 'users',
                localField: 'created_by',
                foreignField: '_id',
                as: 'created_by',
            },
        },
        // Lookup for user who linked the entity
        {
            $lookup: {
                from: 'users',
                localField: 'linked_by',
                foreignField: '_id',
                as: 'linked_by',
            },
        },
        // Lookup for entity company
        {
            $lookup: {
                from: 'entitycompanies',
                localField: 'entity_company',
                foreignField: '_id',
                as: 'entity_company',
            },
        },
    ];
}

/**
 * Builds the match stage with all filters
 */
function buildMatchStage(params) {
    const {
        search,
        status,
        userCompany,
        name,
        type,
        address,
        geostamp,
        uen,
        entity_company,
        created_by,
        linked_to_type,
        linked_to_name,
        linked_to_company,
        linked_to_company_uen,
        driver_action,
        driver,
        container,
        vehicle,
        eir_id,
        created_at,
        updated_at,
    } = params;

    const matchStage: any = { $match: {} };
    const andConditions = [];

    // Add text search regex pattern
    const searchRegex = search
        ? { $regex: search.trim(), $options: 'i' }
        : null;

    // Add company filter if user has company
    if (userCompany) {
        const orConditions =
            status !== ENTITY_STATUS.APPROVED
                ? [
                      { 'linked_receipts.created_by_company': userCompany },
                      { 'unlinked_receipts.created_by_company': userCompany },
                  ]
                : [
                      { created_by_company: userCompany },
                      { created_by_company: null },
                  ];

        andConditions.push({ $or: orConditions });
    }

    // Add direct field filters
    if (name) matchStage.$match.name = { $regex: name.trim(), $options: 'i' };
    if (type) matchStage.$match.type = type;
    if (address)
        matchStage.$match.address = { $regex: address.trim(), $options: 'i' };
    if (uen) matchStage.$match.uen = { $regex: uen.trim(), $options: 'i' };
    if (entity_company) {
        matchStage.$match['entity_company.name'] = {
            $regex: entity_company.trim(),
            $options: 'i',
        };
    }

    // Add creator filter
    if (created_by) {
        andConditions.push({
            $or: [
                {
                    'created_by.fullname': {
                        $regex: created_by.trim(),
                        $options: 'i',
                    },
                },
                {
                    'created_by.email': {
                        $regex: created_by.trim(),
                        $options: 'i',
                    },
                },
            ],
        });
    }

    // Add linked entity filters
    if (linked_to_name) {
        matchStage.$match['linked_to.name'] = {
            $regex: linked_to_name.trim(),
            $options: 'i',
        };
    }
    if (linked_to_type) {
        matchStage.$match['linked_to.type'] = linked_to_type;
    }
    if (linked_to_company) {
        matchStage.$match['linked_to.entity_company.name'] = {
            $regex: linked_to_company.trim(),
            $options: 'i',
        };
    }
    if (linked_to_company_uen) {
        matchStage.$match['linked_to.entity_company.uen'] = {
            $regex: linked_to_company_uen.trim(),
            $options: 'i',
        };
    }

    // Add receipt-related filters
    if (driver_action) {
        andConditions.push({
            $or: [
                { 'linked_receipts.0.driver_action': driver_action },
                { 'unlinked_receipts.0.driver_action': driver_action },
            ],
        });
    }
    if (container) {
        andConditions.push({
            $or: [
                {
                    'linked_receipts.0.container.unique_id': {
                        $regex: container.trim(),
                        $options: 'i',
                    },
                },
                {
                    'unlinked_receipts.0.container.unique_id': {
                        $regex: container.trim(),
                        $options: 'i',
                    },
                },
            ],
        });
    }
    if (vehicle) {
        andConditions.push({
            $or: [
                {
                    'linked_receipts.0.vehicle_number': {
                        $regex: vehicle.trim(),
                        $options: 'i',
                    },
                },
                {
                    'unlinked_receipts.0.vehicle_number': {
                        $regex: vehicle.trim(),
                        $options: 'i',
                    },
                },
            ],
        });
    }
    if (eir_id) {
        andConditions.push({
            $or: [
                {
                    'linked_receipts.0.unique_id': {
                        $regex: eir_id.trim(),
                        $options: 'i',
                    },
                },
                {
                    'unlinked_receipts.0.unique_id': {
                        $regex: eir_id.trim(),
                        $options: 'i',
                    },
                },
            ],
        });
    }
    if (driver) {
        andConditions.push({
            $or: [
                {
                    'linked_receipts.0.driver.username': {
                        $regex: driver.trim(),
                        $options: 'i',
                    },
                },
                {
                    'unlinked_receipts.0.driver.username': {
                        $regex: driver.trim(),
                        $options: 'i',
                    },
                },
                {
                    'linked_receipts.0.driver.fullname': {
                        $regex: driver.trim(),
                        $options: 'i',
                    },
                },
                {
                    'unlinked_receipts.0.driver.fullname': {
                        $regex: driver.trim(),
                        $options: 'i',
                    },
                },
            ],
        });
    }
    if (geostamp) {
        const geostampRegex = new RegExp(geostamp.trim(), 'i');
        andConditions.push({
            $or: [
                { 'linked_receipts.0.location.address': geostampRegex },
                { 'unlinked_receipts.0.location.address': geostampRegex },
            ],
        });
    }

    // Add date range filters
    addDateFilter(matchStage.$match, 'created_at', created_at);
    addDateFilter(matchStage.$match, 'updated_at', updated_at);

    // Add general search filter
    if (search) {
        andConditions.push({
            $or: [
                { name: searchRegex },
                { uen: searchRegex },
                { address: searchRegex },
                // Linked receipts search
                { 'linked_receipts.surveyor.name': searchRegex },
                { 'linked_receipts.surveyor.uen': searchRegex },
                { 'linked_receipts.endorser.uen': searchRegex },
                { 'linked_receipts.endorser.name': searchRegex },
                { 'linked_receipts.container.unique_id': searchRegex },
                { 'linked_receipts.unique_id': searchRegex },
                { 'linked_receipts.vehicle_number': searchRegex },
                // Unlinked receipts search
                { 'unlinked_receipts.surveyor.name': searchRegex },
                { 'unlinked_receipts.surveyor.uen': searchRegex },
                { 'unlinked_receipts.endorser.uen': searchRegex },
                { 'unlinked_receipts.endorser.name': searchRegex },
                { 'unlinked_receipts.container.unique_id': searchRegex },
                { 'unlinked_receipts.unique_id': searchRegex },
                { 'unlinked_receipts.vehicle_number': searchRegex },
                // Linked entity search
                { 'linked_to.uen': searchRegex },
                // Created by search
                { 'created_by.email': searchRegex },
                { 'created_by.fullname': searchRegex },
            ],
        });
    }

    // Add status filter
    if (status) matchStage.$match.status = status;

    // Add all AND conditions if any exist
    if (andConditions.length > 0) {
        matchStage.$match.$and = andConditions;
    }

    return matchStage;
}

/**
 * Helper function to add date range filters
 */
function addDateFilter(matchObj, field, dateString) {
    if (!dateString) return;

    const [startDate, endDate] = dateString.split(',');

    if (startDate && endDate) {
        // Date range
        matchObj[field] = {
            $gte: getMoment(startDate).startOf('date').toDate(),
            $lte: getMoment(endDate).endOf('date').toDate(),
        };
    } else if (startDate) {
        // Single date
        matchObj[field] = {
            $gte: getMoment(startDate).startOf('date').toDate(),
            $lte: getMoment(startDate).endOf('date').toDate(),
        };
    }
}

/**
 * Builds stage to convert arrays to single objects
 */
function buildConversionStage() {
    return {
        $addFields: {
            created_by: { $arrayElemAt: ['$created_by', 0] },
            linked_by: { $arrayElemAt: ['$linked_by', 0] },
            entity_company: { $arrayElemAt: ['$entity_company', 0] },
            linked_to: { $arrayElemAt: ['$linked_to', 0] },
        },
    };
}

/**
 * Builds stage to remove sensitive fields
 */
function buildProjectionStage() {
    return {
        $project: {
            linkedEntityCompany: 0,
            'created_by.password': 0,
            'created_by.salt': 0,
            'created_by.token': 0,
            'linked_by.password': 0,
            'linked_by.salt': 0,
            'linked_by.token': 0,
        },
    };
}

/**
 * Builds pagination stage with metadata
 */
function buildPaginationStage(page, limit) {
    return {
        $facet: {
            metadata: [{ $count: 'total' }],
            data: [{ $skip: (+page - 1) * +limit }, { $limit: +limit }],
        },
    };
}

/**
 * Enriches entity results with mapped S3 URLs
 */
async function enrichEntityResults(entities) {
    if (!entities || entities.length === 0) return;

    await Promise.all(
        entities.map(async (entity) => {
            if (entity.linked_receipts?.length > 0) {
                entity.linked_receipts = await mapUnlinkedSignatureURL(
                    entity.linked_receipts,
                );
            }
            if (entity.unlinked_receipts?.length > 0) {
                entity.unlinked_receipts = await mapUnlinkedSignatureURL(
                    entity.unlinked_receipts,
                );
            }
        }),
    );
}

async function getReceipts({ entityIds, ids }) {
    console.log('LOG-entityIds', entityIds);
    const receipts = await models.Receipt.find({
        $or: [{ entity: { $in: entityIds } }, { _id: { $in: ids } }],
    })
        .populate('container', 'unique_id iso_code')
        .populate('damages', 'position side region type photos')
        .populate('driver', 'username fullname email nric')
        .populate('created_by', 'username fullname email nric')
        .lean();
    return mapS3UrlReceipts(receipts);
}

export async function getEntity(req, res) {
    const { _id } = req.params;

    // Add filter by created_by_company if user has company
    const created_by_company =
        req.user && req.user.company ? req.user.company : null;

    // Build query object with _id
    const query: any = { _id };

    // Add company filter if user has company
    if (created_by_company) {
        // query.created_by_company = created_by_company;
    }

    const entity = await models.Entity.findOne(query)
        .populate('created_by', 'username fullname email nric')
        .populate('linked_by', 'username fullname email nric')
        .populate('entity_company', 'name uen address')
        .populate('linked_to', 'name uen')
        .lean();
    if (!entity) return res.notFound();
    return res.ok(entity);
}

export async function createEntity(req, res) {
    const {
        name,
        type,
        location,
        address,
        uen,
        block_house_number,
        entity_company,
        building_name,
        status,
        street_name,
        postal_code,
        unit,
    } = matchedData(req);

    // Check for existing entity with same name and address
    const existingEntity = await models.Entity.findOne({
        name: name,
        address: address,
        $or: [
            { created_by_company: req.user.company },
            { created_by_company: null },
        ],
        deleted_at: { $exists: false },
    });

    if (existingEntity) {
        return res.badRequest(
            'Entity with the same name and address already exists',
        );
    }

    const createData: any = {
        name,
        type,
        address,
        block_house_number,
        building_name,
        uen,
        entity_company,
        status,
        street_name,
        postal_code,
        unit,
        created_by: req.user.id,
        created_by_company: req.user.company,
    };

    console.log('LOG-createData', createData);
    if (entity_company) {
        const entityCompany = await models.EntityCompany.findOne({
            _id: entity_company,
        });
        if (!entityCompany) return res.badRequest('Invalid entity company');
        createData.uen = entityCompany.uen;
    }

    if (location && location.latitude && location.longitude) {
        createData.location = {
            type: 'Point',
            coordinates: [location?.longitude, location?.latitude],
        };
    }

    const entity = await models.Entity.create(createData);
    return res.ok(entity);
}

export async function updateEntity(req, res) {
    const { _id } = req.params;
    const {
        name,
        type,
        location,
        address,
        unit,
        postal_code,
        entity_company,
        saved_emails,
        uen,
        status,
        block_house_number,
    } = matchedData(req);

    // First check if the entity exists at all
    const entityExists = await models.Entity.findOne({ _id });
    if (!entityExists) return res.notFound();

    // Check if the user has permissions to update this entity using the entity we already retrieved
    if (
        req.user &&
        req.user.company &&
        entityExists.created_by_company &&
        entityExists.created_by_company.toString() !==
            req.user.company.toString()
    ) {
        return res.forbidden(
            'You do not have permission to update this entity',
        );
    }

    const updateEntity: any = {
        updated_by: req.user.id,
    };

    // Only update fields that are provided
    if (name !== undefined) updateEntity.name = name;
    if (type !== undefined) updateEntity.type = type;
    if (address !== undefined) updateEntity.address = address;
    if (saved_emails !== undefined) updateEntity.saved_emails = saved_emails;
    if (unit !== undefined) updateEntity.unit = unit;
    if (postal_code !== undefined) updateEntity.postal_code = postal_code;
    if (status !== undefined) updateEntity.status = status;
    if (uen !== undefined) updateEntity.uen = uen;
    // Only validate and use entity_company if provided
    if (entity_company) {
        const entityCompany = await models.EntityCompany.findOne({
            _id: entity_company,
        });
        if (!entityCompany) return res.badRequest('Invalid entity company');

        updateEntity.entity_company = entity_company;
        updateEntity.uen = entityCompany.uen;
    }
    if (location && location.latitude && location.longitude) {
        updateEntity.location = {
            type: 'Point',
            coordinates: [location?.longitude, location?.latitude],
        };
    }
    if (block_house_number !== undefined) {
        updateEntity.block_house_number = block_house_number;
    }

    await models.Entity.updateOne({ _id }, updateEntity);
    const updatedEntity = await models.Entity.findOne({ _id });
    return res.ok(updatedEntity);
}

export async function updateEntityStatus(req, res) {
    const { _id } = req.params;
    const { status } = matchedData(req);
    const entity = await models.Entity.findOne({
        _id,
    });

    if (!entity) return res.notFound();

    await models.Entity.updateOne({ _id }, { status, updated_by: req.user.id });
    const updatedEntity = await models.Entity.findOne({ _id });
    return res.ok(updatedEntity);
}

export async function linkEntity(req, res) {
    const { link_from, link_to } = matchedData(req);

    // Check if the target entity exists
    const linkedToEntity = await models.Entity.findOne({
        _id: link_to,
    });

    if (!linkedToEntity || linkedToEntity.status !== ENTITY_STATUS.APPROVED)
        return res.badRequest('link to invalid entity');

    // Check if user has company and if the target entity belongs to their company
    if (
        req.user &&
        req.user.company &&
        linkedToEntity.created_by_company &&
        linkedToEntity.created_by_company.toString() !==
            req.user.company.toString()
    ) {
        return res.forbidden(
            'You do not have permission to link to this entity',
        );
    }

    const updateData = {
        linked_to: link_to,
        status: ENTITY_STATUS.LINKED,
        linked_by: req.user.id,
        updated_by: req.user.id,
        linked_receipts: [],
    };

    // Check company permissions for each source entity
    for (const entityId of link_from) {
        const entity = await models.Entity.findOne({ _id: entityId });

        if (!entity) {
            return res.badRequest(`Entity ${entityId} not found`);
        }

        // Check if user has company and if the source entity belongs to their company
        if (
            req.user &&
            req.user.company &&
            entity.created_by_company &&
            entity.created_by_company.toString() !== req.user.company.toString()
        ) {
            return res.forbidden(
                `You do not have permission to link entity ${entityId}`,
            );
        }
    }

    await Promise.all(
        link_from.map(async (entityId: string) => {
            const entity = await models.Entity.findOne({
                _id: entityId,
            });
            let receiptIds = [];
            if (entity.status === ENTITY_STATUS.LINKED) {
                receiptIds = entity.linked_receipts;
            } else {
                const receipts = await models.Receipt.find({
                    entity: entityId,
                });
                receiptIds = _.map(receipts, '_id');
                await Promise.all(
                    receipts.map(async (receipt) => {
                        await updateReceiptEntity(receipt, linkedToEntity);
                    }),
                );
            }

            updateData.linked_receipts = receiptIds;
            console.log('LOG-updateData', updateData);
            await models.Entity.updateOne({ _id: entityId }, updateData);
        }),
    );

    return res.ok();
}

/**
 * Soft delete: mark entity as deleted, update receipts to pending entity.
 */
export async function deleteEntitySoft(req, res) {
    const { _id } = req.params;

    // First check if entity exists at all
    const entityExists = await models.Entity.findOne({ _id });
    if (!entityExists) return res.notFound();

    // Check if user has permission to delete this entity
    if (
        req.user &&
        req.user.company &&
        entityExists.created_by_company?.toString() !==
            req.user.company?.toString()
    ) {
        return res.forbidden(
            'You do not have permission to delete this entity',
        );
    }

    // create pending entity
    const pendingEntity = await models.Entity.create({});

    await models.Entity.updateOne(
        { _id },
        {
            deleted_at: new Date(),
            updated_by: req.user.id,
        },
    );

    // find all receipts linked to entity
    await models.Receipt.updateMany(
        {
            entity: _id,
        },
        {
            entity: pendingEntity._id,
        },
    );

    return res.ok();
}

/**
 * Hard delete: remove entity from DB, update receipts to pending entity.
 */
export async function deleteEntityHard(req, res) {
    const { _id } = req.params;

    // First check if entity exists at all
    const entityExists = await models.Entity.findOne({ _id });
    if (!entityExists) return res.notFound();

    // Check if user has permission to delete this entity
    if (
        req.user &&
        req.user.company &&
        entityExists.created_by_company?.toString() !==
            req.user.company?.toString()
    ) {
        return res.forbidden(
            'You do not have permission to delete this entity',
        );
    }

    // create pending entity
    const pendingEntity = await models.Entity.create({});

    // Remove the entity from DB
    await models.Entity.deleteOne({ _id });

    // find all receipts linked to entity
    await models.Receipt.updateMany(
        {
            entity: _id,
        },
        {
            entity: pendingEntity._id,
        },
    );

    return res.ok();
}
