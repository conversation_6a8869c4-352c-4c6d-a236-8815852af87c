import {
    getRoadNameInReceipt,
    mapS3UrlReceipts,
} from '@src/routes/mobile/services/receipt.service';
import { CTRClient, CTRClientUAT } from '@src/utils/api';
import { matchedData } from 'express-validator';
import { models, isValidObjectId } from 'mongoose';
import { getReceiptFromCode } from '../service/receipt.service';

// Remove zlib import and promisify setup

export async function getReceipts(req, res) {
    const {
        search = '',
        limit = 10,
        page = 1,
        sortBy = '_id',
        sort = 1,
        eir_id, // eir_id
        endorser, // endorser.name || endorser.nric
        surveyor, // surveyor.name || surveyor.nric
        created_at, // created_at
        updated_at, // updated_at
        vehicle_number, // vehicle_number
        iso_code, // container.iso_code
        container, // container.unique_id
        entity, // entity.name
        driver_action, // driver_action
        created_by, // created_by.fullname || created_by.email
        type = '',
        facility_company, // facility_company.name
        transporter_company, // transporter_company.name
    } = req.query;

    // Add filter by created_by_company if user has company
    const created_by_company =
        req.user && req.user.company ? req.user.company : null;

    // Create search regex pattern once to reuse
    const searchRegex = search
        ? { $regex: search.trim(), $options: 'i' }
        : null;

    // Build aggregation pipeline
    const aggregationPipeline: any = [
        // First, add all the necessary lookups to populate related collections
        {
            $lookup: {
                from: 'containers',
                localField: 'container',
                foreignField: '_id',
                as: 'container_data',
            },
        },
        {
            $lookup: {
                from: 'users',
                localField: 'created_by',
                foreignField: '_id',
                as: 'created_by_data',
            },
        },
        {
            $lookup: {
                from: 'entities',
                localField: 'entity',
                foreignField: '_id',
                as: 'entity_data',
            },
        },
        {
            $lookup: {
                from: 'damages',
                localField: 'damages',
                foreignField: '_id',
                as: 'damages_data',
            },
        },
        {
            $lookup: {
                from: 'groups',
                localField: 'transporter_company',
                foreignField: '_id',
                as: 'transporter_company_data',
            },
        },
        {
            $lookup: {
                from: 'entitycompanies',
                localField: 'facility_company',
                foreignField: '_id',
                as: 'facility_company_data',
            },
        },
        // Convert arrays to single objects
        {
            $addFields: {
                container: { $arrayElemAt: ['$container_data', 0] },
                created_by: { $arrayElemAt: ['$created_by_data', 0] },
                entity: { $arrayElemAt: ['$entity_data', 0] },
                transporter_company: {
                    $arrayElemAt: ['$transporter_company_data', 0],
                },
                facility_company: {
                    $arrayElemAt: ['$facility_company_data', 0],
                },
            },
        },
        // Remove the temporary arrays
        {
            $project: {
                container_data: 0,
                created_by_data: 0,
                entity_data: 0,
                transporter_company_data: 0,
                facility_company_data: 0,
                'created_by.password': 0,
                'created_by.salt': 0,
                'created_by.token': 0,
            },
        },
        // Match stage with all filters
        {
            $match: {},
        },
    ];

    // Initialize match conditions
    const matchConditions: any = {};

    // Filter by created_by_company if it exists
    if (created_by_company) {
        matchConditions.created_by_company = created_by_company;
    }

    if (type) {
        // If type is provided, filter by type
        matchConditions.type = type;
    }

    // Basic search conditions
    if (search) {
        matchConditions.$or = matchConditions.$or || [];
        matchConditions.$or.push(
            { unique_id: searchRegex },
            { vehicle_number: searchRegex },
            { 'endorser.name': searchRegex },
            { 'endorser.nric': searchRegex },
            { 'surveyor.name': searchRegex },
            { 'surveyor.nric': searchRegex },
            { 'container.unique_id': searchRegex },
            { 'container.iso_code': searchRegex },
            { 'entity.name': searchRegex },
            { 'created_by.fullname': searchRegex },
            { 'created_by.email': searchRegex },
            { 'transporter_company.name': searchRegex },
            { 'facility_company.name': searchRegex },
        );
    }

    // Apply specific field filters
    if (eir_id) {
        matchConditions.unique_id = { $regex: eir_id, $options: 'i' };
    }

    if (endorser) {
        matchConditions.$or = matchConditions.$or || [];
        matchConditions.$or.push(
            { 'endorser.name': { $regex: endorser, $options: 'i' } },
            { 'endorser.nric': { $regex: endorser, $options: 'i' } },
        );
    }

    if (surveyor) {
        matchConditions.$or = matchConditions.$or || [];
        matchConditions.$or.push(
            { 'surveyor.name': { $regex: surveyor, $options: 'i' } },
            { 'surveyor.nric': { $regex: surveyor, $options: 'i' } },
        );
    }

    if (vehicle_number) {
        matchConditions.vehicle_number = {
            $regex: vehicle_number,
            $options: 'i',
        };
    }

    if (container) {
        matchConditions['container.unique_id'] = {
            $regex: container,
            $options: 'i',
        };
    }

    if (iso_code) {
        matchConditions['container.iso_code'] = {
            $regex: iso_code,
            $options: 'i',
        };
    }

    if (entity) {
        matchConditions['entity.name'] = { $regex: entity, $options: 'i' };
    }

    if (driver_action) {
        matchConditions.driver_action = driver_action;
    }

    if (created_by) {
        matchConditions.$or = matchConditions.$or || [];
        matchConditions.$or.push(
            { 'created_by.fullname': { $regex: created_by, $options: 'i' } },
            { 'created_by.email': { $regex: created_by, $options: 'i' } },
        );
    }

    // Filter by company fields
    if (facility_company) {
        matchConditions['facility_company.name'] = {
            $regex: facility_company,
            $options: 'i',
        };
    }

    if (transporter_company) {
        matchConditions['transporter_company.name'] = {
            $regex: transporter_company,
            $options: 'i',
        };
    }

    // Handle date filters
    if (created_at) {
        // Parse date range if provided in format 'YYYY-MM-DD,YYYY-MM-DD'
        const [startDate, endDate] = created_at.split(',');
        console.log('[startDate, endDate] for created_at:', [
            startDate,
            endDate,
        ]);

        if (startDate && endDate) {
            // If date range is provided
            const endOfDay = new Date(endDate);
            endOfDay.setHours(23, 59, 59, 999);

            matchConditions.created_at = {
                $gte: new Date(startDate),
                $lte: endOfDay,
            };
        } else if (startDate) {
            // If only one date is provided, search for that specific day
            const start = new Date(startDate);
            const end = new Date(startDate);
            end.setHours(23, 59, 59, 999);
            matchConditions.created_at = { $gte: start, $lte: end };
        }
    }

    if (updated_at) {
        // Parse date range if provided in format 'YYYY-MM-DD,YYYY-MM-DD'
        const [startDate, endDate] = updated_at.split(',');
        console.log('[startDate, endDate] for updated_at:', [
            startDate,
            endDate,
        ]);

        if (startDate && endDate) {
            // If date range is provided
            const endOfDay = new Date(endDate);
            endOfDay.setHours(23, 59, 59, 999);

            matchConditions.updated_at = {
                $gte: new Date(startDate),
                $lte: endOfDay,
            };
        } else if (startDate) {
            // If only one date is provided, search for that specific day
            const start = new Date(startDate);
            const end = new Date(startDate);
            end.setHours(23, 59, 59, 999);
            matchConditions.updated_at = { $gte: start, $lte: end };
        }
    }

    // Add match conditions to the pipeline
    aggregationPipeline[8].$match = matchConditions;

    // Add sorting
    aggregationPipeline.push({
        $sort: {
            [sortBy]: +sort,
        },
    });

    // Add pagination using facet
    aggregationPipeline.push({
        $facet: {
            metadata: [{ $count: 'total' }],
            data: [{ $skip: (+page - 1) * +limit }, { $limit: +limit }],
        },
    });

    // Execute the aggregation pipeline
    const receipts = await models.Receipt.aggregate(aggregationPipeline);

    // Format response
    return res.ok({
        items: receipts[0].data || [],
        total: receipts[0].metadata[0]?.total || 0,
        page: +page,
        limit: +limit,
    });
}

export async function getReceipt(req, res) {
    const { _id } = req.params;

    // Add filter by created_by_company if user has company
    const created_by_company =
        req.user && req.user.company ? req.user.company : null;

    // Build query object with _id
    const query: any = { _id };

    // Add company filter if user has company
    if (created_by_company) {
        query.created_by_company = created_by_company;
    }

    // check _id is mongoose id
    const receipt = await models.Receipt.findOne(query)
        .populate('driver', 'fullname username nric')
        .populate('container', 'unique_id iso_code')
        .populate('created_by', 'username email fullname nric vehicle_number')
        .populate('damages')
        .populate('entity', 'name type uen saved_emails')
        .populate('transporter_company', 'name code groupInfo')
        .populate('facility_company', 'name')
        .lean();

    if (!receipt) return res.notFound();

    const receiptUrls = await mapS3UrlReceipts([receipt]);

    if (receiptUrls.length) {
        const receiptWithRoadName = await getRoadNameInReceipt(receiptUrls[0]);
    }
    return res.ok(receiptUrls[0]);
}

export async function getShareReceipt(req, res) {
    const { code } = req.params;
    const receipt = await getReceiptFromCode(code);
    if (!receipt) return res.notFound();

    const receiptUrls = await mapS3UrlReceipts([receipt]);
    return res.ok(receiptUrls[0]);
}

export async function sendReceiptMail(req, res) {
    // Extract emails from form-data
    let emails = [];

    // When using form-data, field names like "emails[0]" appear directly as keys
    if (req.body) {
        // Extract all form-data fields that match the pattern "emails[index]"
        const emailFields = Object.keys(req.body).filter(
            (key) => key.startsWith('emails[') && key.endsWith(']'),
        );

        if (emailFields.length > 0) {
            // Get email values from these fields
            emails = emailFields.map((field) => req.body[field]);
        }
        // Fallback to other formats if needed
        else if (req.body.emails) {
            if (Array.isArray(req.body.emails)) {
                emails = req.body.emails;
            } else if (typeof req.body.emails === 'string') {
                emails = req.body.emails
                    .split(',')
                    .map((email) => email.trim());
            }
        }
    }

    // Filter out empty values
    emails = emails.filter((email) => email && email.trim());

    // Log the extracted emails for debugging
    console.log('Extracted emails:', emails);

    // Validate that we have at least one email
    if (!emails.length) {
        return res.badRequest('At least one valid email is required');
    }

    const { _id } = req.params;

    // Get the PDF file from the request
    if (!req.file) {
        return res.badRequest('pdfFile is required');
    }

    const pdfBuffer = req.file.buffer;
    const pdfFileName = req.file.originalname || 'eEIR-report.pdf';

    try {
        // Log original file size
        console.log(
            `PDF size: ${pdfBuffer.length} bytes (${(
                pdfBuffer.length / 1024
            ).toFixed(2)} KB)`,
        );

        // Convert buffer to base64 string directly - no compression
        const base64Content = pdfBuffer.toString('base64');

        // Log base64 encoded size
        console.log(
            `Base64 encoded size: ${base64Content.length} bytes (${(
                base64Content.length / 1024
            ).toFixed(2)} KB)`,
        );

        // Get the receipt with needed populated data
        const receipt = await models.Receipt.findOne({ _id })
            .populate('container', 'unique_id')
            .populate('entity')
            .populate('transporter_company', 'name code groupInfo')
            .populate('facility_company', 'name')
            .lean();

        if (!receipt) return res.notFound('Receipt not found');

        // Format date and time from created_at
        const createdDate = new Date(receipt.created_at);
        // Convert to Singapore Time (SGT / UTC+8)
        const formattedDate = createdDate.toLocaleDateString('en-US', {
            year: 'numeric',
            month: 'short',
            day: 'numeric',
            timeZone: 'Asia/Singapore',
        });
        const formattedTime = createdDate.toLocaleTimeString('en-US', {
            hour: '2-digit',
            minute: '2-digit',
            timeZone: 'Asia/Singapore',
        });

        // Construct email subject and body
        const subject = `CDAS eEIR Report: ${receipt.unique_id}`;
        const message = `
            <html>
            <head>
            <style>
            body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
            .container { max-width: 600px; margin: 0; padding: 20px; }
            .header { font-size: 18px; font-weight: bold; margin-bottom: 20px; }
            .info-row { margin-bottom: 10px; }
            .label { font-weight: bold; }
            </style>
            </head>
            <body>
            <div class="container">
            <div class="header">eEIR Report Details</div>
            <div class="info-row"><span class="label">eEIR submission date:</span> ${formattedDate}</div>
            <div class="info-row"><span class="label">eEIR submission time:</span> ${formattedTime}</div>
            <div class="info-row"><span class="label">Container number:</span> ${
                receipt.container?.unique_id || 'N/A'
            }</div>
            <div class="info-row"><span class="label">Handing over:</span> 
                ${receipt.surveyor?.name || 'N/A'} 
                (${receipt.surveyor?.owner_name || 'N/A'})
            </div>
            <div class="info-row"><span class="label">Taking over:</span> 
                ${receipt.endorser?.name || 'N/A'} 
                (${receipt.endorser?.owner_name || 'N/A'})
            </div>
            </div>
            </body>
            </html>
        `;

        // Send email with original PDF as base64 attachment
        const response =
            process.env.NODE_ENV !== 'production'
                ? await CTRClientUAT.sendEmail({
                      message,
                      subject,
                      emails,
                      attachments: {
                          filename: pdfFileName,
                          content: base64Content,
                          encoding: 'base64',
                          contentType: 'application/pdf',
                      },
                  })
                : await CTRClient.sendEmail({
                      message,
                      subject,
                      emails,
                      attachments: {
                          filename: pdfFileName,
                          content: base64Content,
                          encoding: 'base64',
                          contentType: 'application/pdf',
                      },
                  });

        return res.ok({ message: response });
    } catch (error) {
        console.error('Error sending PDF:', error);

        // Forward the actual error from the 3rd party service
        if (error && typeof error === 'object' && 'status' in error && 'data' in error) {
            // This is a structured error from the API client
            return res.status(error.status || 500).json({
                status: 'error',
                error_message: error.data?.message || error.statusText || 'Failed to send email',
                error_code: error.status || 500,
                data: error.data || null,
            });
        }

        // Fallback for unexpected errors
        return res.badRequest('Failed to process PDF file');
    }
}

export async function shareLink(req, res) {
    const { _id } = req.params;
    const receipt = await models.Receipt.findOne({ _id }).lean();
    if (!receipt) return res.notFound();
    const code = receipt.unique_id.toString();
    const shareCode = Buffer.from(code).toString('base64');
    console.log('LOG-shareCode', shareCode);

    const originalId = Buffer.from(shareCode, 'base64').toString('ascii');
    console.log('LOG-originalId', originalId);

    return res.ok({ code: shareCode });
}
