import { matchedData } from 'express-validator';
import models from '@models';

/**
 * Get all licenses with pagination and search
 */
export async function listLicenses(req, res) {
    try {
        const { search, limit = 10, page = 1 } = matchedData(req);
        const skip = (page - 1) * limit;

        const query: any = {};
        if (search) {
            query.name = { $regex: search, $options: 'i' };
        }

        const [licenses, total] = await Promise.all([
            models.License.find(query)
                .sort({ created_at: -1 })
                .skip(skip)
                .limit(limit),
            models.License.countDocuments(query),
        ]);

        return res.ok({
            licenses,
            pagination: {
                total,
                page,
                limit,
                totalPages: Math.ceil(total / limit),
            },
        });
    } catch (error) {
        console.error('Error listing licenses:', error);
        return res.serverInternalError('Error retrieving licenses');
    }
}

/**
 * Get specific license by ID
 */
export async function getLicense(req, res) {
    try {
        const { id } = matchedData(req);

        const license = await models.License.findById(id);
        if (!license) {
            return res.notFound('License not found');
        }

        return res.ok(license);
    } catch (error) {
        console.error('Error getting license:', error);
        return res.serverInternalError('Error retrieving license');
    }
}

/**
 * Create new license
 */
export async function createLicense(req, res) {
    try {
        const { name, status = true, tag = false } = matchedData(req);

        // Check if license name already exists
        const existingLicense = await models.License.findOne({ name });
        if (existingLicense) {
            return res.badRequest('License name already exists');
        }

        const license = await models.License.create({
            name,
            status,
            tag,
        });

        return res.ok(license);
    } catch (error) {
        console.error('Error creating license:', error);
        if (error.code === 11000) {
            return res.badRequest('License name already exists');
        }
        return res.serverInternalError('Error creating license');
    }
}

/**
 * Update license
 */
export async function updateLicense(req, res) {
    try {
        const { id, name, status, tag } = matchedData(req);

        const license = await models.License.findById(id);
        if (!license) {
            return res.notFound('License not found');
        }

        // Check if new name conflicts with existing license
        if (name && name !== license.name) {
            const existingLicense = await models.License.findOne({ name });
            if (existingLicense) {
                return res.badRequest('License name already exists');
            }
        }

        const updateData: any = {};
        if (name !== undefined) updateData.name = name;
        if (status !== undefined) updateData.status = status;
        if (tag !== undefined) updateData.tag = tag;

        const updatedLicense = await models.License.findByIdAndUpdate(
            id,
            updateData,
            { new: true },
        );

        return res.ok(updatedLicense);
    } catch (error) {
        console.error('Error updating license:', error);
        if (error.code === 11000) {
            return res.badRequest('License name already exists');
        }
        return res.serverInternalError('Error updating license');
    }
}

/**
 * Delete license
 */
export async function deleteLicense(req, res) {
    try {
        const { id } = matchedData(req);

        // Check if license exists
        const license = await models.License.findById(id);
        if (!license) {
            return res.notFound('License not found');
        }

        // Check if license is being used in any subscriptions
        const subscriptionsCount = await models.Subscription.countDocuments({
            license: id,
        });
        if (subscriptionsCount > 0) {
            return res.badRequest(
                'Cannot delete license that is being used in subscriptions',
            );
        }

        await models.License.findByIdAndDelete(id);

        return res.ok({ message: 'License deleted successfully' });
    } catch (error) {
        console.error('Error deleting license:', error);
        return res.serverInternalError('Error deleting license');
    }
}
