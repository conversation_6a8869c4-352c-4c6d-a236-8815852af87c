import { normalizeUserProfile } from '@src/routes/normalize';
import { models } from 'mongoose';

export async function listUser(req, res) {
    const { search = '', limit = 10, page = 1 } = req.query;
    const query: any = {
        $or: [
            { fullname: { $regex: search.trim(), $options: 'i' } },
            { email: { $regex: search.trim(), $options: 'i' } },
            { mobile: { $regex: search.trim(), $options: 'i' } },
            { username: { $regex: search.trim(), $options: 'i' } },
            { nric: { $regex: search.trim(), $options: 'i' } },
        ],
    };
    if (req.query.company) {
        query.company = req.query.company;
    } else if (req.user && req.user.company) {
        // Check if requester has company and add company filter
        query.company = req.user.company;
    }

    const users = await models.User.find(query)
        .populate('company', 'name')
        .populate('roleAcl', 'name')
        .limit(limit)
        .skip((page - 1) * limit)
        .sort({ name: 1 })
        .lean();
    const total = await models.User.countDocuments(query);
    return res.ok({
        items: users.map((user: any) => {
            return normalizeUserProfile(user);
        }),
        total,
        page: +page,
        limit: +limit,
    });
}
