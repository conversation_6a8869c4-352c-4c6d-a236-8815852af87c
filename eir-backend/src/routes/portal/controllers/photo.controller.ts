import models from '@src/models';
import { getSignedUrlForS3 } from '@src/utils/s3';

export async function getPhotos(req, res) {
    const {
        search = '',
        limit = 10,
        page = 1,
        sortBy = '_id',
        sort = 1,
    } = req.query;

    const query = {};
    const photos = await models.Photo.find(query)
        .populate('damage')
        .populate('receipt')
        .sort({ [sortBy]: sort }) // 1 for ascending, -1 for descending
        .limit(limit)
        .skip((page - 1) * limit)
        .lean();

    const total = await models.Photo.countDocuments(query);
    return res.ok({
        items: photos,
        total,
        page,
        limit,
    });
}

export async function getPhoto(req, res) {
    const { _id } = req.params;
    const photo = await models.Photo.findById(_id, 'key location').lean();
    const photo_link = await getSignedUrlForS3(photo.key, 3600);
    return res.ok({ ...photo, photo_link });
}
