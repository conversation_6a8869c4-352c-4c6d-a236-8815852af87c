import { ENTITY_STATUS } from '@src/constants/logic';
import { matchedData } from 'express-validator';
import _ from 'lodash';
import { models } from 'mongoose';
import { updateReceiptEntity } from '../service/receipt.service';
import { mapS3UrlReceipts } from '@src/routes/mobile/services/receipt.service';
import { OneMapClient } from '@src/utils/api';

export async function getOneMapToken(req, res) {
    const access_token = await OneMapClient.getToken();
    return res.ok({ access_token });
}
