import { models } from 'mongoose';

export async function list<PERSON><PERSON>ney(req, res) {
    const { search = '', limit = 10, page = 1 } = req.query;
    const query = {
        name: { $regex: search, $options: 'i' },
    };
    const companies = await models.Journey.find(query)
        .limit(limit)
        .skip((page - 1) * limit)
        .lean();
    const total = await models.Journey.countDocuments(query);
    return res.ok({ items: companies, total, page: +page, limit: +limit });
}
