import { matchedData } from 'express-validator';
import models from '@models';

/**
 * Get all subscriptions with pagination and filtering
 */
export async function listSubscriptions(req, res) {
    try {
        const {
            search,
            company,
            status,
            limit = 10,
            page = 1,
        } = matchedData(req);
        const skip = (page - 1) * limit;

        const query: any = {};
        if (search) {
            // Search in company name or license name
            const companies = await models.Group.find({
                name: { $regex: search, $options: 'i' },
            }).select('_id');
            const licenses = await models.License.find({
                name: { $regex: search, $options: 'i' },
            }).select('_id');

            query.$or = [
                { company: { $in: companies.map((c) => c._id) } },
                { license: { $in: licenses.map((l) => l._id) } },
            ];
        }
        if (company) query.company = company;
        if (status) query.status = status;

        const [subscriptions, total] = await Promise.all([
            models.Subscription.find(query)
                .populate('company', 'name')
                .populate('license', 'name status')
                .populate('allowed_users', 'username fullname email')
                .sort({ created_at: -1 })
                .skip(skip)
                .limit(limit),
            models.Subscription.countDocuments(query),
        ]);

        return res.ok({
            subscriptions,
            pagination: {
                total,
                page,
                limit,
                totalPages: Math.ceil(total / limit),
            },
        });
    } catch (error) {
        console.error('Error listing subscriptions:', error);
        return res.serverInternalError('Error retrieving subscriptions');
    }
}

/**
 * Get specific subscription by ID
 */
export async function getSubscription(req, res) {
    try {
        const { id } = matchedData(req);

        const subscription = await models.Subscription.findById(id)
            .populate('company', 'name')
            .populate('license', 'name status')
            .populate('allowed_users', 'username fullname email')
            .populate('created_by', 'username')
            .populate('updated_by', 'username');

        if (!subscription) {
            return res.notFound('Subscription not found');
        }

        return res.ok(subscription);
    } catch (error) {
        console.error('Error getting subscription:', error);
        return res.serverInternalError('Error retrieving subscription');
    }
}

/**
 * Create new subscription (company license)
 */
export async function createSubscription(req, res) {
    try {
        const {
            company,
            license,
            end_date,
            allowed_users,
            limit_container = 0,
            status = 'active',
        } = matchedData(req);

        // Validate company exists
        const companyExists = await models.Group.findById(company);
        if (!companyExists) {
            return res.badRequest('Company not found');
        }

        // Validate license exists
        const licenseExists = await models.License.findById(license);
        if (!licenseExists) {
            return res.badRequest('License not found');
        }

        // Validate users exist
        if (allowed_users && allowed_users.length > 0) {
            const usersCount = await models.User.countDocuments({
                _id: { $in: allowed_users },
            });
            if (usersCount !== allowed_users.length) {
                return res.badRequest('One or more users not found');
            }
        }

        // Check if company already has an active subscription
        const existingSubscription = await models.Subscription.findOne({
            company,
            status: 'active',
        });

        if (existingSubscription) {
            return res.badRequest('Company already has an active subscription');
        }

        const subscription = await models.Subscription.create({
            company,
            license,
            end_date: new Date(end_date),
            allowed_users: allowed_users || [],
            limit_container,
            status,
            created_by: req.user.id,
        });

        const populatedSubscription = await models.Subscription.findById(
            subscription._id,
        )
            .populate('company', 'name')
            .populate('license', 'name status')
            .populate('allowed_users', 'username fullname email');

        return res.ok();
    } catch (error) {
        console.error('Error creating subscription:', error);
        return res.serverInternalError('Error creating subscription');
    }
}

/**
 * Update subscription
 */
export async function updateSubscription(req, res) {
    try {
        const {
            id,
            company,
            license,
            end_date,
            allowed_users,
            limit_container,
            status,
        } = matchedData(req);

        const subscription = await models.Subscription.findById(id);
        if (!subscription) {
            return res.notFound('Subscription not found');
        }

        // Validate company if provided
        if (company && company !== subscription.company.toString()) {
            const companyExists = await models.Group.findById(company);
            if (!companyExists) {
                return res.badRequest('Company not found');
            }
        }

        // Validate license if provided
        if (license && license !== subscription.license.toString()) {
            const licenseExists = await models.License.findById(license);
            if (!licenseExists) {
                return res.badRequest('License not found');
            }
        }

        // Validate users if provided
        if (allowed_users && allowed_users.length > 0) {
            const usersCount = await models.User.countDocuments({
                _id: { $in: allowed_users },
            });
            if (usersCount !== allowed_users.length) {
                return res.badRequest('One or more users not found');
            }
        }

        const updateData: any = { updated_by: req.user.id };
        if (company !== undefined) updateData.company = company;
        if (license !== undefined) updateData.license = license;
        if (end_date !== undefined) updateData.end_date = new Date(end_date);
        if (allowed_users !== undefined)
            updateData.allowed_users = allowed_users;
        if (limit_container !== undefined)
            updateData.limit_container = limit_container;
        if (status !== undefined) updateData.status = status;

        const updatedSubscription = await models.Subscription.findByIdAndUpdate(
            id,
            updateData,
            { new: true },
        )
            .populate('company', 'name')
            .populate('license', 'name status')
            .populate('allowed_users', 'username fullname email');

        return res.ok(updatedSubscription);
    } catch (error) {
        console.error('Error updating subscription:', error);
        return res.serverInternalError('Error updating subscription');
    }
}

/**
 * Update users in subscription (replaces the entire allowed_users array)
 */
export async function updateSubscriptionUsers(req, res) {
    try {
        const { id, allowed_users } = matchedData(req);

        const subscription = await models.Subscription.findById(id);
        if (!subscription) {
            return res.notFound('Subscription not found');
        }

        // Check if users exist (if allowed_users is provided)
        if (allowed_users && allowed_users.length > 0) {
            const usersCount = await models.User.countDocuments({
                _id: { $in: allowed_users },
            });
            if (usersCount !== allowed_users.length) {
                return res.badRequest('One or more users not found');
            }
        }

        // Update the allowed_users array (replace entirely)
        const updatedSubscription = await models.Subscription.findByIdAndUpdate(
            id,
            {
                allowed_users: allowed_users || [],
                updated_by: req.user.id,
            },
            { new: true },
        )
            .populate('company', 'name')
            .populate('license', 'name status')
            .populate('allowed_users', 'username fullname email');

        return res.ok(updatedSubscription);
    } catch (error) {
        console.error('Error updating subscription users:', error);
        return res.serverInternalError('Error updating subscription users');
    }
}

/**
 * Delete subscription
 */
export async function deleteSubscription(req, res) {
    try {
        const { id } = matchedData(req);

        const subscription = await models.Subscription.findById(id);
        if (!subscription) {
            return res.notFound('Subscription not found');
        }

        await models.Subscription.findByIdAndDelete(id);

        return res.ok({ message: 'Subscription deleted successfully' });
    } catch (error) {
        console.error('Error deleting subscription:', error);
        return res.serverInternalError('Error deleting subscription');
    }
}

/**
 * Get subscription by company
 */
export async function getSubscriptionByCompany(req, res) {
    try {
        const { companyId } = matchedData(req);

        const subscription = await models.Subscription.findOne({
            company: companyId,
        })
            .populate('company', 'name')
            .populate('license', 'name status')
            .populate('allowed_users', 'username fullname email')
            .sort({ created_at: -1 });

        if (!subscription) {
            return res.notFound('No subscription found for this company');
        }

        return res.ok(subscription);
    } catch (error) {
        console.error('Error getting subscription by company:', error);
        return res.serverInternalError('Error retrieving subscription');
    }
}

export async function checkSubscription(req, res) {
    const { user } = req;
    const subscription = await models.Subscription.findOne({
        company: user.company,
        start_date: { $lte: new Date() },
        end_date: { $gte: new Date() },
    });
    if (!subscription) {
        return res.forbidden('Subscription not found');
    }
    return res.ok(subscription);
}
