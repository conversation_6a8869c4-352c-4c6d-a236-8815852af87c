import { CTRClient } from '@src/utils/api';
import models from '@models';
import { normalizeUserProfile } from '@src/routes/normalize';

export async function validateToken(req, res) {
    try {
        const token = req.headers.authorization;

        if (!token) {
            return res.unauthorized();
        }

        const data = await CTRClient.verifyToken(token);
        if (!data) {
            return res.unauthorized();
        }

        const { user, accessToken } = data;
        if (!user) res.unauthorized();
        if (user) {
            const existingUser = await models.User.findOne({
                username: user.username,
            });

            if (existingUser) {
                delete user._id;
                await models.User.updateOne(
                    { username: user.username },
                    {
                        token: accessToken,
                        ...user,
                    },
                );
            } else {
                await models.User.create({ ...user, token: accessToken });
            }

            const updatedUser = await models.User.findOne({
                username: user.username,
            })
                .populate('roleAcl')
                .populate('company');

            // Check license validation for portal users with company assignments
            if (updatedUser.company) {
                const subscription = await models.Subscription.findOne({
                    company: updatedUser.company._id,
                    status: 'active',
                }).populate('license');

                if (!subscription) {
                    console.log(
                        'LOG-No active license found for company',
                        updatedUser.company._id,
                    );
                    return res.forbidden(
                        'No active license found for your company',
                    );
                }

                // Check if subscription is expired
                const currentDate = new Date();
                if (
                    subscription.end_date &&
                    subscription.end_date < currentDate
                ) {
                    // Update subscription status to expired
                    await models.Subscription.updateOne(
                        { _id: subscription._id },
                        { status: 'expired' },
                    );
                    console.log(
                        'LOG-Company license expired',
                        updatedUser.company._id,
                    );
                    return res.forbidden('Company license has expired');
                }

                // Check if user is in the allowed users list
                const isUserAllowed = subscription.allowed_users.some(
                    (allowedUserId) =>
                        allowedUserId.toString() === updatedUser._id.toString(),
                );

                if (!isUserAllowed) {
                    console.log(
                        'LOG-User not in allowed users list',
                        updatedUser._id,
                    );
                    return res.forbidden(
                        'User is not authorized to access EIR with current company license',
                    );
                }

                // Check if license is active
                if (!subscription.license.status) {
                    console.log(
                        'LOG-License inactive',
                        subscription.license._id,
                    );
                    return res.forbidden('License is currently inactive');
                }

                console.log(
                    'LOG-License validation passed for portal user',
                    updatedUser._id,
                );
            }

            return res.ok(normalizeUserProfile(updatedUser));
        }

        return res.unauthorized();
    } catch (error) {
        console.error(error);
        return res.serverInternalError(error.message);
    }
}
