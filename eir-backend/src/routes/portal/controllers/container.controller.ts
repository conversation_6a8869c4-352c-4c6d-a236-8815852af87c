import { models } from 'mongoose';
import { mapS3UrlDamages } from '../service/damage.service';

export async function listContainer(req, res) {
    
        const { search = '', limit = 10, page = 1 } = req.query;
        console.log('LOG-search', search);
        const query = {
            $or: [
                { iso_code: { $regex: search, $options: 'i' } },
                { unique_id: { $regex: search, $options: 'i' } },
            ],
        };
        const companies = await models.Container.find(query)
            .limit(limit)
            .skip((page - 1) * limit)
            .lean();
        const total = await models.Container.countDocuments(query);
        return res.ok({ items: companies, total, page: +page, limit: +limit });

}

export async function getContainer(req, res) {
    
        const { _id } = req.params;
        const container = await models.Container.findOne({
            _id,
        }).lean();

        if (!container) return res.notFound();

        const lastReceipt = await models.Receipt.findOne({
            container: container._id,
        })
            .sort({ date: -1 })
            .populate('damages')
            .lean();

        return res.ok({
            ...container,
            damages: lastReceipt?.damages?.length
                ? await mapS3UrlDamages(lastReceipt.damages)
                : [],
        });

}
