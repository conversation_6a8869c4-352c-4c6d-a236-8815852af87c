import { Route } from '@src/interfaces/routes';
import { validateRequest } from '@src/middlewares';
import * as controllers from '@src/routes/portal/controllers/entity-company.controller';
import * as commonValidations from '@src/validation/common';

export const authRoute: Route[] = [
    {
        method: 'post',
        path: '/',
        cb: controllers.createCompanyEntity,
        middleware: [
            commonValidations.string('name'),
            commonValidations.string('address'),
            commonValidations.string('uen'),
            commonValidations.string('postal_code'),
            commonValidations.string('unit'),
            commonValidations.string('street_name').optional(),
            commonValidations.string('building_name').optional(),
            commonValidations.string('block_house_number').optional(),
            commonValidations.object('bizfile').optional(),
            commonValidations.object('location').optional(),
            validateRequest,
        ],
    },
    {
        method: 'get',
        path: '/check',
        cb: controllers.getCompanyEntityByUen,
        middleware: [commonValidations.string('uen'), validateRequest],
    },
];

export const publicRoute: Route[] = [];
