import { Route } from '@src/interfaces/routes';
import * as controllers from '@src/routes/portal/controllers/damage.controller';
import * as commonValidations from '@src/validation/common';

export const authRoute: Route[] = [
	{
		method: 'get',
		path: '/',
		cb: controllers.getDamages,
		middleware: [],
	},
	{
		method: 'get',
		path: '/:_id',
		cb: controllers.getDamage,
		middleware: [],
	},
	{
		method: 'get',
		path: '/download-damage-report',
		cb: controllers.downloadDamageReport,
		middleware: [
			commonValidations.array('ids', { require: false }),
		],
	}
];

export const publicRoute: Route[] = [];
