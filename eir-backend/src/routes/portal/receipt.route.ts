import { Route } from '@src/interfaces/routes';
import { validateRequest } from '@src/middlewares';
import * as controllers from '@src/routes/portal/controllers/receipt.controller';
import * as commonValidations from '@src/validation/common';
import multer from 'multer';

// Configure multer for memory storage
const upload = multer({ 
    storage: multer.memoryStorage(),
    limits: {
        fileSize: 10 * 1024 * 1024, // 10MB limit
    },
    fileFilter: (req, file, cb) => {
        if (file.mimetype === 'application/pdf') {
            cb(null, true);
        } else {
            cb(new Error('Only PDF files are allowed'));
        }
    }
});

export const authRoute: Route[] = [
    {
        method: 'get',
        path: '/',
        cb: controllers.getReceipts,
        middleware: [],
    },
    {
        method: 'get',
        path: '/:_id',
        cb: controllers.getReceipt,
        middleware: [],
    },
    {
        method: 'post',
        path: '/:_id/send-email',
        cb: controllers.sendReceiptMail,
        middleware: [
            upload.single('pdfFile'),
            validateRequest,
        ],
    },
    {
        method: 'get',
        path: '/:_id/share-link',
        cb: controllers.shareLink,
        middleware: [],
    },
];

export const publicRoute: Route[] = [
    {
        method: 'get',
        path: '/share/:code',
        cb: controllers.getShareReceipt,
        middleware: [],
    },
];
