import { Route } from '@src/interfaces/routes';
import * as controllers from '@src/routes/portal/controllers/license.controller';
import * as commonValidations from '@src/validation/common';
import { validateRequest } from '@src/middlewares';

export const authRoute: Route[] = [
    // Get all licenses
    {
        method: 'get',
        path: '/',
        cb: controllers.listLicenses,
        middleware: [
            commonValidations.string('search').optional(),
            commonValidations
                .number('limit')
                .optional()
                .custom((value) => value > 0),
            commonValidations
                .number('page')
                .optional()
                .custom((value) => value > 0),
            validateRequest,
        ],
    },
    // Get specific license
    {
        method: 'get',
        path: '/:id',
        cb: controllers.getLicense,
        middleware: [
            commonValidations.string('id'),
            validateRequest,
        ],
    },
    // Create new license
    {
        method: 'post',
        path: '/',
        cb: controllers.createLicense,
        middleware: [
            commonValidations.string('name'),
            commonValidations.boolean('status').optional(),
            commonValidations.boolean('tag').optional(),
            validateRequest,
        ],
    },
    // Update license
    {
        method: 'patch',
        path: '/:id',
        cb: controllers.updateLicense,
        middleware: [
            commonValidations.string('id'),
            commonValidations.string('name').optional(),
            commonValidations.boolean('status').optional(),
            commonValidations.boolean('tag').optional(),
            validateRequest,
        ],
    },
    // Delete license
    {
        method: 'delete',
        path: '/:id',
        cb: controllers.deleteLicense,
        middleware: [
            commonValidations.string('id'),
            validateRequest,
        ],
    },
];

export const publicRoute: Route[] = [];
