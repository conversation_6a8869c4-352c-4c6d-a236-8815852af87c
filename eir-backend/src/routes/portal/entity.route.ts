import { Route } from '@src/interfaces/routes';
import { validateRequest } from '@src/middlewares';
import * as controllers from '@src/routes/portal/controllers/entity.controller';
import * as commonValidations from '@src/validation/common';

export const authRoute: Route[] = [
    {
        method: 'get',
        path: '/',
        cb: controllers.getListEntity,
        middleware: [],
    },
    {
        method: 'get',
        path: '/:_id',
        cb: controllers.getEntity,
        middleware: [commonValidations.string('_id'), validateRequest],
    },
    {
        method: 'post',
        path: '/',
        cb: controllers.createEntity,
        middleware: [
            commonValidations.string('name'),
            commonValidations.string('type'),
            commonValidations.string('address'),
            commonValidations.string('unit').optional(),
            commonValidations.string('block_house_number').optional(),
            commonValidations.string('postal_code').optional(),
            commonValidations.string('building_name').optional(),
            commonValidations.string('street_name').optional(),
            commonValidations.string('status'),
            commonValidations.string('uen'),
            commonValidations.string('entity_company').isMongoId().optional(),
            commonValidations.object('location').optional(),
            validateRequest,
        ],
    },
    {
        method: 'patch',
        path: '/:_id',
        cb: controllers.updateEntity,
        middleware: [
            commonValidations.string('_id'),
            commonValidations.string('name').optional(),
            commonValidations.string('type').optional(),
            commonValidations.string('address').optional(),
            commonValidations.string('status').optional(),
            commonValidations.string('uen').optional(),
            commonValidations.string('unit').optional(),
            commonValidations.string('postal_code').optional(),
            commonValidations.string('block_house_number').optional(),
            commonValidations.string('entity_company').isMongoId().optional(),
            commonValidations.object('location').optional(),
            commonValidations
                .array('saved_emails', { require: false })
                .optional(),
            validateRequest,
        ],
    },
    {
        method: 'patch',
        path: '/:_id/status',
        cb: controllers.updateEntityStatus,
        middleware: [
            commonValidations.string('_id'),
            commonValidations.string('status').optional(),
            validateRequest,
        ],
    },
    {
        method: 'post',
        path: '/assign',
        cb: controllers.linkEntity,
        middleware: [
            commonValidations.array('link_from', { require: true }),
            commonValidations.string('link_to'),
            validateRequest,
        ],
    },
    {
        method: 'delete',
        path: '/:_id',
        cb: controllers.deleteEntityHard,
        middleware: [commonValidations.string('_id'), validateRequest],
    },
];

export const publicRoute: Route[] = [];
