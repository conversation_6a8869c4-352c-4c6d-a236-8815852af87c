import { Route } from '@src/interfaces/routes';
import * as controllers from '@src/routes/portal/controllers/subscription.controller';
import * as commonValidations from '@src/validation/common';
import { validateRequest } from '@src/middlewares';

export const authRoute: Route[] = [
    // Get all subscriptions
    {
        method: 'get',
        path: '/',
        cb: controllers.listSubscriptions,
        middleware: [
            commonValidations.string('search').optional(),
            commonValidations.string('company').optional(),
            commonValidations.string('status').optional(),
            commonValidations
                .number('limit')
                .optional()
                .custom((value) => value > 10),
            commonValidations
                .number('page')
                .optional()
                .custom((value) => value > 0),
            validateRequest,
        ],
    },
    // Get specific subscription
    {
        method: 'get',
        path: '/:id',
        cb: controllers.getSubscription,
        middleware: [commonValidations.string('id'), validateRequest],
    },
    // Create new subscription (company license)
    {
        method: 'post',
        path: '/',
        cb: controllers.createSubscription,
        middleware: [
            commonValidations.string('company'),
            commonValidations.string('license'),
            commonValidations.string('end_date'),
            commonValidations.array('allowed_users', { required: true }),
            commonValidations.number('limit_container').optional(),
            commonValidations.string('status').optional(),
            validateRequest,
        ],
    },
    // Update subscription
    {
        method: 'patch',
        path: '/:id',
        cb: controllers.updateSubscription,
        middleware: [
            commonValidations.string('id'),
            commonValidations.string('company').optional(),
            commonValidations.string('license').optional(),
            commonValidations.string('end_date').optional(),
            commonValidations
                .array('allowed_users', { required: true })
                .optional(),
            commonValidations.number('limit_container').optional(),
            commonValidations.string('status').optional(),
            validateRequest,
        ],
    },
    // Update users in subscription
    {
        method: 'patch',
        path: '/:id/users',
        cb: controllers.updateSubscriptionUsers,
        middleware: [
            commonValidations.string('id'),
            commonValidations.array('allowed_users', { required: false }),
            validateRequest,
        ],
    },
    // Delete subscription
    {
        method: 'delete',
        path: '/:id',
        cb: controllers.deleteSubscription,
        middleware: [commonValidations.string('id'), validateRequest],
    },
    // Get subscription by company
    {
        method: 'get',
        path: '/company/:companyId',
        cb: controllers.getSubscriptionByCompany,
        middleware: [commonValidations.string('companyId'), validateRequest],
    },
];

export const publicRoute: Route[] = [];
