import { Route } from '@src/interfaces/routes';
import { validateRequest } from '@src/middlewares';
import * as controllers from '@src/routes/portal/controllers/container.controller';
import * as commonValidations from '@src/validation/common';

export const authRoute: Route[] = [
    {
        method: 'get',
        path: '/',
        cb: controllers.listContainer,
        middleware: [],
    },
    {
        method: 'get',
        path: '/:_id',
        cb: controllers.getContainer,
        middleware: [],
    }
];

export const publicRoute: Route[] = [];
