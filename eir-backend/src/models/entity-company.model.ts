import { Document, Model, model, Schema } from 'mongoose';
import { IEntityCompany } from '@interfaces/models';

const entityCompanySchema = new Schema<IEntityCompany>(
    {
        name: {
            type: String,
            required: true,
        },
        uen: {
            type: String,
            required: true,
        },
        address: {
            type: String,
            required: false,
        },
        unit: {
            type: String,
            required: false,
        },
        block_house_number: {
            type: String,
            required: false,
        },
        bizfile: {
            type: Object,
            required: false,
        },
        street_name: {
            type: String,
            required: false,
        },
        building_name: {
            type: String,
            required: false,
        },
        postal_code: {
            type: String,
            required: false,
        },
        created_by: {
            type: Schema.Types.ObjectId,
            ref: 'User',
        },
        updated_by: {
            type: Schema.Types.ObjectId,
            ref: 'User',
        },
    },
    {
        timestamps: { createdAt: 'created_at', updatedAt: 'updated_at' },
        versionKey: false,
    },
);

entityCompanySchema.index({ uen: 1 }, { unique: true });

entityCompanySchema.index(
    { location: '2dsphere' },
    {
        partialFilterExpression: { location: { $exists: true } },
    },
);

interface IEntityCompanyModel extends Model<IEntityCompany> {}

export default model<IEntityCompany & Document, IEntityCompanyModel>(
    'EntityCompany',
    entityCompanySchema,
);
