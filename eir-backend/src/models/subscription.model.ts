import { ISubscription } from '@src/interfaces/models';
import { Document, Model, Schema, model } from 'mongoose';

const subscriptionSchema = new Schema<ISubscription>(
    {
        company: {
            type: Schema.Types.ObjectId,
            ref: 'Group',
            required: true,
        },
        license: {
            type: Schema.Types.ObjectId,
            ref: 'License',
            required: true,
        },
        limit_container: {
            type: Number,
            default: 0,
        },
        end_date: {
            type: Date,
            required: true,
        },
        allowed_users: [
            {
                type: Schema.Types.ObjectId,
                ref: 'User',
            },
        ],
        status: {
            type: String,
            enum: ['active', 'inactive', 'expired'],
            default: 'active',
        },
        created_by: {
            type: Schema.Types.ObjectId,
            ref: 'User',
        },
        updated_by: {
            type: Schema.Types.ObjectId,
            ref: 'User',
        },
        company_sub_updated_at: {
            type: Date,
        },
        company_sub_updated_by: {
            type: Schema.Types.ObjectId,
            ref: 'User',
        },
    },
    {
        timestamps: true,
    },
);

interface ISubscriptionModel extends Model<ISubscription> {}

export default model<ISubscription & Document, ISubscriptionModel>(
    'Subscription',
    subscriptionSchema,
);
