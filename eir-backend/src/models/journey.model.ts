import { <PERSON><PERSON><PERSON><PERSON> } from '@src/interfaces/models';
import { Document, Model, Schema, model, models } from 'mongoose';
import { JOURNEY_TYPE, JOURNEY_STATUS } from '@src/constants/logic';

const journeySchema = new Schema<I<PERSON><PERSON><PERSON>>(
    {
        start_receipt: {
            type: Schema.Types.ObjectId,
            ref: 'Receipt',
            required: false,
        },
        end_receipt: {
            type: Schema.Types.ObjectId,
            ref: 'Receipt',
            required: false,
        },
        receipts: [
            {
                type: Schema.Types.ObjectId,
                ref: 'Receipt',
                required: false,
            },
        ],
        container: {
            type: Schema.Types.ObjectId,
            ref: 'Container',
            required: false,
        },
        status: {
            type: String,
            enum: Object.values(JOURNEY_STATUS),
            default: JOURNEY_STATUS.ACTIVE,
            required: false,
        },
        type: {
            type: String,
            enum: Object.values(JOURNEY_TYPE),
            required: false,
        },
    },
    {
        timestamps: { createdAt: 'created_at', updatedAt: 'updated_at' },
        versionKey: false,
    },
);


interface IJourneyModel extends Model<IJourney> {}

export default model<IJourney & Document, IJourneyModel>(
    'Journey',
    journeySchema,
);