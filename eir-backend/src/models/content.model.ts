import { Document, Schema, model, Model } from 'mongoose';
import { IContent } from '@interfaces/models';

const LANG = {
    EN: 'en',
    ZH: 'zh',
};
interface IContentModel extends Model<IContent> {}

const schema = new Schema<IContent>(
    {
        lang: { type: String, required: true, enum: Object.values(LANG), default: LANG.EN },
        data: { type: Schema.Types.Mixed, required: true, default: {} },
    },
    { timestamps: { createdAt: false, updatedAt: 'updated_at' } },
);

export default model<IContent, IContentModel>('Content', schema);
