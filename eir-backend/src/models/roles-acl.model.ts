import { IModule, IRoleAcl } from '@src/interfaces/models';
import { Schema, model, models, Document, Model } from 'mongoose';

const moduleSchema = new Schema<IModule>({
    name: { type: String, required: true },
    allowCreate: { type: Boolean, default: false },
    allowRead: { type: Boolean, default: false },
    allowUpdate: { type: Boolean, default: false },
    allowDelete: { type: Boolean, default: false }
}, { _id: false });

const roleAclSchema = new Schema<IRoleAcl>({
    name: { type: String, required: true, unique: true },
    forCdas: { type: Boolean, default: false },
    modules: [moduleSchema],
    createdBy: { type: Schema.Types.ObjectId, ref: 'User' },
    updatedBy: { type: Schema.Types.ObjectId, ref: 'User' }
}, {
    timestamps: true
});

// This is necessary to avoid model compilation errors in watch mode
// see https://mongoosejs.com/docs/api/connection.html#connection_Connection-deleteModel
if (models.rolesAcl) {
    delete models.rolesAcl;
}


interface IRoleAclModel extends Model<IRoleAcl> {}

export default model<IRoleAcl & Document, IRoleAclModel>(
    'RolesAcl',
    roleAclSchema,
);