import { Document, Model, model, Schema } from 'mongoose';
import { IUser } from '@interfaces/models';

const userSchema = new Schema<IUser>(
    {
        username: String,
        email: String,
        fullname: String,
        mobile: String,
        group: String,
        company: {
            type: Schema.Types.ObjectId,
            ref: 'Group',
        },
        token: String,
        token_expire_at: Date,
        role: String,
        roleAcl: {
            type: Schema.Types.ObjectId,
            ref: 'RolesAcl',
        },
        vehicle_number: String,
        nric: String,
        signature: String,
    },
    {
        timestamps: { createdAt: 'created_at', updatedAt: 'updated_at' },
        versionKey: false,
    },
);


interface IUserModel extends Model<IUser> {}

export default model<IUser & Document, IUserModel>('User', userSchema);
