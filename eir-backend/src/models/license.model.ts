import { ILicense } from '@src/interfaces/models';
import { Document, Model, Schema, model, models } from 'mongoose';

const licenseSchema = new Schema<ILicense>(
    {
        name: {
            type: String,
            unique: true,
            required: true,
        },
        status: {
            type: Boolean,
            default: true,
        },
        tag: {
            // for default or not
            type: Boolean,
            default: false,
        },
    },
    {
        timestamps: { createdAt: 'created_at', updatedAt: 'updated_at' },
        versionKey: false,
    },
);


interface ILicenseModel extends Model<ILicense> {}

export default model<ILicense & Document, ILicenseModel>(
    'License',
    licenseSchema,
);