import { Document, Schema, model, Model } from 'mongoose';
import { PERMISSION, ROLE } from '@src/constants/app';
import { IAdmin } from '@interfaces/models';


const adminSchema = new Schema<IAdmin>(
    {
        username: {
            type: String,
            required: true,
        },
        password: String,
        role: {
            type: String,
            default: ROLE.SUPER_ADMIN,
        },
        permission: {
            type: [String],
            default: Object.values(PERMISSION),
        },
        token: String,
        locked_at: Date,
    },
    {
        timestamps: { createdAt: 'created_at', updatedAt: 'updated_at' },
    },
);

interface IAdminModel extends Model<IAdmin> {
    getAllAdmins: () => Promise<IAdmin[]>;
}

const Collection = model<IAdmin, IAdminModel>('Admin', adminSchema);
export default Collection;
