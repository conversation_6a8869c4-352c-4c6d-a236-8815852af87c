import { Document, Schema, model, Model } from 'mongoose';
import { PERMISSION, ROLE } from '@src/constants/app';
import { IPhoto } from '@interfaces/models';


const photoSchema = new Schema<IPhoto>(
    {
        key: { type: String, required: true },
        location: {
            type: { type: String },
            coordinates: { type: [Number], required: false },
        },
        building_name: { type: String },
        block: { type: String },
        street_name: { type: String },
        portal_code: { type: String },
        receipt: { type: Schema.Types.ObjectId, ref: 'Receipt' },
        damage: { type: Schema.Types.ObjectId, ref: 'Damage' },
        created_by: { type: Schema.Types.ObjectId, ref: 'User' },
    },
    {
        timestamps: { createdAt: 'created_at', updatedAt: 'updated_at' },
    },
);

photoSchema.index({ key: 1 }, { unique: true });

interface IPhotoModel extends Model<IPhoto> {}

export default model<IPhoto, IPhotoModel>('Photo', photoSchema);
