import { Document, Model, model, Schema } from 'mongoose';
import { ISetting } from '@interfaces/models';

const settingSchema = new Schema<ISetting>(
    {

    },
    {
        timestamps: { createdAt: 'created_at', updatedAt: 'updated_at' },
        versionKey: false,
    },
);

settingSchema.index({ timestamp: 1 });

interface ISettingModel extends Model<ISetting> {}

export default model<ISetting & Document, ISettingModel>('Setting', settingSchema);
