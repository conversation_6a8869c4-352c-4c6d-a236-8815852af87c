import { Document, Model, model, Schema, Types } from 'mongoose';
import { IDamage } from '@interfaces/models';

const damageSchema = new Schema<IDamage>(
    {
        unique_id: { type: String, required: false, unique: true },
        date: { type: Date, default: new Date() },
        receipt: { type: Types.ObjectId, ref: 'Receipt', required: false },
        container: { type: Types.ObjectId, ref: 'Container', required: true },
        position: { type: String, required: false },
        side: { type: String, required: true },
        region: { type: String, required: true },
        type: { type: String, required: true },
        photos: { type: [String], required: false },
        parent: { type: Types.ObjectId, ref: 'Damage', required: false },
        created_by_company: {
            type: Schema.Types.ObjectId,
            ref: 'Group',
            required: false,
        },
    },
    {
        timestamps: { createdAt: 'created_at', updatedAt: 'updated_at' },
        versionKey: false,
    },
);

damageSchema.index({ container: 1 });
damageSchema.index({ receipt: 1 });

interface IDamageModel extends Model<IDamage> {}

export default model<IDamage & Document, IDamageModel>('Damage', damageSchema);
