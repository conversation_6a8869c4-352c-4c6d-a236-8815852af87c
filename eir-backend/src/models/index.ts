import Admin from '@src/models/admin.model';
import AdminLog from '@src/models/adminLog.model';
import Content from '@src/models/content.model';
import Receipt from '@src/models/receipt.model';
import Setting from '@src/models/setting.model';
import Damage from '@src/models/damage.model';
import User from '@src/models/user.model';
import Container from '@src/models/container.model';
import Entity from '@src/models/entity.model';
import Group from '@src/models/group.model';
import RolesAcl from './roles-acl.model';
import License from './license.model';
import Subscription from './subscription.model';
import EntityCompany from './entity-company.model';
import Photo from './photo.model';
import Journey from './journey.model';

const models = {
    Admin,
    AdminLog,
    Content,

    Receipt,
    Setting,
    Damage,
    User,
    Container,
    Entity,
    EntityCompany,
    Group,
    RolesAcl,
    License,
    Subscription,
    Photo,
    Journey,
};


export default models;
