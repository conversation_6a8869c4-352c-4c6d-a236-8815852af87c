import { Document, Model, model, Schema } from 'mongoose';
import { IReceipt } from '@interfaces/models';
import {
    CONTAINER_LOADING,
    DRIVER_ACTION,
    ENTITY_ACTION,
    RECEIPT_TYPE,
} from '@src/constants/logic';

const receiptSchema = new Schema<IReceipt>(
    {
        date: { type: Date, default: new Date() },
        unique_id: { type: String, required: true },
        container: {
            type: Schema.Types.ObjectId,
            ref: 'Container',
            required: true,
        },
        container_loading: {
            type: String,
            enum: Object.values(CONTAINER_LOADING),
        },
        damages: {
            type: [Schema.Types.ObjectId],
            ref: 'Damage',
            required: false,
        },
        photos: { type: [String], required: false },
        type: { type: String, enum: Object.values(RECEIPT_TYPE) }, // use enum ['import', 'export', 'local']

        driver: { type: Schema.Types.ObjectId, ref: 'User', required: true },
        driver_signature: String,
        driver_action: {
            type: String,
            enum: Object.values(DRIVER_ACTION),
        },
        vehicle_number: String,

        entity: { type: Schema.Types.ObjectId, ref: 'Entity', required: true },
        entity_signature: String,
        entity_action: {
            type: String,
            enum: Object.values(ENTITY_ACTION),
        },
        entity_on_behalf: Boolean,

        location: {
            _id: false,
            type: { type: String, enum: ['Point', 'LineString', 'Polygon'] },
            coordinates: { type: [Number], required: false },
            address: { type: String, required: false },
            onemap_data: { type: Schema.Types.Mixed, required: false },
        },
        endorser: {
            name: { type: String, required: false },
            nric: { type: String, required: false },
            owner_name: { type: String, required: false },
            uen: { type: String, required: false },
            signature: { type: String, required: false },
            label: { type: String, required: false },
            title: { type: String, required: false },
            data: { type: Schema.Types.Mixed, required: false },
        },
        surveyor: {
            name: { type: String, required: false },
            nric: { type: String, required: false },
            owner_name: { type: String, required: false },
            uen: { type: String, required: false },
            signature: { type: String, required: false },
            label: { type: String, required: false },
            title: { type: String, required: false },
            data: { type: Schema.Types.Mixed, required: false },
        },

        created_by: {
            type: Schema.Types.ObjectId,
            ref: 'User',
            required: true,
        },
        created_by_company: {
            type: Schema.Types.ObjectId,
            ref: 'Group',
            required: true,
        },
        facility_company: {
            type: Schema.Types.ObjectId,
            ref: 'EntityCompany',
            required: false,
        },
        transporter_company: {
            type: Schema.Types.ObjectId,
            ref: 'Group',
            required: false,
        },
        company: String,
        journey: String,
    },
    {
        timestamps: { createdAt: 'created_at', updatedAt: 'updated_at' },
        versionKey: false,
    },
);

receiptSchema.index({ unique_id: 1 }, { unique: true });

interface IReceiptModel extends Model<IReceipt> {}

export default model<IReceipt & Document, IReceiptModel>(
    'Receipt',
    receiptSchema,
);
