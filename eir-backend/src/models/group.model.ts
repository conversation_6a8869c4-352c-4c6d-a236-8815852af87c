import { Document, Model, model, Schema } from 'mongoose';
import { IGroup } from '@interfaces/models';

const groupSchema = new Schema<IGroup>(
    {
        name: {
            type: String,
            required: true,
        },
        code: { type: String, uppercase: true },
        contactPerson: {
            name: { type: String },
            phone: { type: String },
        },
        groupInfo: {
            uen: { type: String },
            registrationDate: { type: Date, default: Date.now() },
            address: { type: String },
            postalCode: { type: String },
            phone: { type: String },
            email: { type: String },
        },
        status: {
            type: String,
            enum: ['inactive', 'active', 'suspended', 'terminated'],
        },
        parent: {
            type: Schema.Types.ObjectId,
            ref: 'Group',
        },
        organizationType: {
            type: Schema.Types.ObjectId,
            ref: 'RoleAcl',
        },
        logo: { type: String },
        timezone: { type: String, default: '+08:00' },
        level: { type: Number, default: 0 },
        copiedGroupInfo: { type: Boolean, default: false },
        copiedContactPerson: { type: Boolean, default: false },
        createdBy: {
            type: Schema.Types.ObjectId,
            ref: 'User',
        },
        updatedBy: {
            type: Schema.Types.ObjectId,
            ref: 'User',
        },
        trailerShareStatus: { type: Boolean, default: false },
    },
    {
        timestamps: { createdAt: 'created_at', updatedAt: 'updated_at' },
        versionKey: false,
    },
);

groupSchema.index({ location: '2dsphere' }, {
	partialFilterExpression: { location: { $exists: true } },
});

interface IGroupModel extends Model<IGroup> {}

export default model<IGroup & Document, IGroupModel>('Group', groupSchema);
