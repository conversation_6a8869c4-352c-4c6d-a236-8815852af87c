import { Document, Model, model, Schema } from 'mongoose';
import { IEntity } from '@interfaces/models';
import { ENTITY_STATUS, ENTITY_TYPE } from '@src/constants/logic';

const entitySchema = new Schema<IEntity>(
    {
        unique_id: { type: String, required: false },
        uen: { type: String, required: false },
        name: { type: String, required: false },
        address: { type: String, required: false },
        building_name: { type: String, required: false },
        block_house_number: {
            type: String,
            required: false,
        },
        postal_code: { type: String, required: false },
        unit: { type: String, required: false },
        street_name: { type: String, required: false },
        type: {
            type: String,
            enum: Object.values(ENTITY_TYPE),
            required: false,
        },
        status: {
            type: String,
            enum: Object.values(ENTITY_STATUS),
            default: ENTITY_STATUS.PENDING,
            required: false,
        },
        created_by: {
            type: Schema.Types.ObjectId,
            ref: 'User',
            required: false,
        },
        linked_by: {
            type: Schema.Types.ObjectId,
            ref: 'User',
            required: false,
        },
        linked_to: {
            type: Schema.Types.ObjectId,
            ref: 'Entity',
            required: false,
        },
        location: {
            type: { type: String, enum: ['Point', 'LineString', 'Polygon'] },
            coordinates: { type: [Number], required: false },
            _id: false,
        },
        linked_receipts: [
            {
                type: Schema.Types.ObjectId,
                ref: 'Receipt',
                required: false,
            },
        ],
        created_by_company: {
            type: Schema.Types.ObjectId,
            ref: 'Group',
            required: false,
        },
        entity_company: {
            type: Schema.Types.ObjectId,
            ref: 'EntityCompany',
            required: false,
        },
        saved_emails: {
            type: [String],
            required: false,
        },
        is_unknown_entity: { type: Boolean, required: false },
        deleted_at: { type: Date, required: false },
    },
    {
        timestamps: { createdAt: 'created_at', updatedAt: 'updated_at' },
        versionKey: false,
    },
);

// create an index for 3keys uen, name, and address
entitySchema.index({ uen: 1, name: 1, address: 1 });
entitySchema.index(
    { location: '2dsphere' },
    {
        partialFilterExpression: { location: { $exists: true } },
    },
);

interface IEntityModel extends Model<IEntity> {}

export default model<IEntity & Document, IEntityModel>('Entity', entitySchema);
