import { Document, Model, model, Schema } from 'mongoose';
import { IContainer } from '@interfaces/models';

const containerSchema = new Schema<IContainer>(
    {
        unique_id: { type: String, required: true },
        container_number: { type: String, required: true },
        iso_code: String,
        container_type: String,
        container_size: String,
        container_weight: String,
        company: String,
    },
    {
        timestamps: { createdAt: 'created_at', updatedAt: 'updated_at' },
        versionKey: false,
    },
);

containerSchema.index({ container_number: 1, iso_code: 1 }, { unique: true });

interface IContainerModel extends Model<IContainer> {}

const Container = model<IContainer & Document, IContainerModel>('Container', containerSchema);

// Drop the existing index on unique_id if it exists
(async () => {
  try {
    await Container.collection.dropIndex('unique_id_1');
    console.log('Successfully dropped index on unique_id field');
  } catch (error) {
    // If the index doesn't exist, MongoDB will throw an error
    console.log('No index found on unique_id or already removed');
  }
})();

export default Container;
