import { Document, Schema, model } from 'mongoose';

interface AdminLog {
    user_id: string;
    action: string;
    path: string;
    data: object;
}
const adminLogSchema = new Schema(
    {
        user_id: {
            type: String,
            required: true,
        },
        action: {
            type: String,
            required: true,
        },
        path: {
            type: String,
            default: '',
        },
        data: {
            type: Object,
            default: {},
        },
    },
    { versionKey: false, timestamps: { createdAt: 'created_at' } },
);

export default model<AdminLog & Document>('AdminLog', adminLogSchema);
