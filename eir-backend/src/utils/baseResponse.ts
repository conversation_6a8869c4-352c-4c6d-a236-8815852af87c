import { Request, Response, NextFunction } from 'express';
import { IUser } from '@src/interfaces/models';
import { Document } from 'mongoose';
import configs from '@config';

export interface AppRequest {
    key?: string;
    sessionID: string;
    user?: IUser;
    resource: Document;
    attemptsKey: string;
    session: {
        login_attempts: number;
        login_locked_until: Date;
    };
    invalidNumber: boolean;
}

export interface AppResponse extends Response{
    success: any;
    failure: any;
    ok: (data?: any) => void;
    noContent: any;
    badRequest: any;
    errorInfo: any;
    unauthorized: any;
    notFound: any;
    forbidden: any;
    serverInternalError: any;
    rateLimited: any;
}

export interface BaseResponse {
    status: string;
    error_message: string;
    error_code: string | number;
    data: any;
}

export default function (req: Request, res: AppResponse & Response, next: NextFunction) {
    function success(data: any) {
        res.json({
            status: 'success',
            data,
            error_message: '',
            error_code: '',
        });
    }

    function failure(message: string, data: any, statusCode: string | number) {
        res.json({
            status: 'error',
            data: data || null,
            error_message: message,
            error_code: statusCode,
        });
    }

    res.success = success;
    res.failure = failure;

    res.ok = (data) => {
        success(data);
    };

    res.noContent = (message = 'no_content') => {
        success(message);
    };

    res.badRequest = (message = 'invalid', data: any) => {
        failure(message, data, 400);
    };

    res.errorInfo = (info = 'invalid') => {
        failure('bad_request', { info }, 400);
    };

    res.unauthorized = (message = 'unauthorized', data: any) => {
        failure(message, data, 401);
    };

    res.notFound = (message = 'not_found', data: any) => {
        failure(message, data, 404);
    };

    res.forbidden = (message = 'forbidden', data: any) => {
        failure(message, data, 403);
    };

    res.serverInternalError = (message = 'server_internal_error', data = null) => {
        if (configs.app.isProd) {
            message = 'server_internal_error';
            data = null;
        }
        failure(message, data, 500);
    };

    res.rateLimited = (message = 'too_many_requests', data = null) => {
        failure(message, data, 409);
    };

    next();
}

function success(data?: any) {
    return {
        status: 'success',
        data,
        error_message: '',
        error_code: '',
    };
}

function failure(message: string, data: any, statusCode: string | number) {
    return {
        status: 'error',
        data: data || null,
        error_message: message,
        error_code: statusCode,
    };
}

export const socketRes = {
    ok: (data?: any) => {
        return success(data);
    },

    noContent: (message = 'no_content') => {
        return success(message);
    },

    badRequest: (message = 'bad_request', data?: any) => {
        return failure(message, data, 400);
    },

    errorInfo: (info = 'invalid') => {
        return failure('bad_request', { info }, 400);
    },

    unauthorized: (message = 'unauthorized', data: any) => {
        return failure(message, data, 401);
    },

    notFound: (message = 'not_found', data?: any) => {
        return failure(message, data, 404);
    },

    forbidden: (message = 'forbidden', data: any) => {
        return failure(message, data, 403);
    },

    serverInternalError: (message = 'server_internal_error', data = null) => {
        if (configs.app.isProd) {
            message = 'server_internal_error';
            data = null;
        }
        return failure(message, data, 500);
    },

    rateLimited: (message = 'too_many_requests', data = null) => {
        return failure(message, data, 409);
    },
};
