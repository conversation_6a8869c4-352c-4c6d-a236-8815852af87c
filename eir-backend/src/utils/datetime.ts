import moment_tz from 'moment-timezone';
import configs from '@config';
moment_tz.locale('en-sg');

export const ISO_DATE_REGEX = /\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}\.\d{3}Z/g;

export function getMoment(date?: Date | string, tz: string = configs.timezone) {
    return moment_tz(date || new Date()).tz(tz);
}

export function parseDate(date, format, tz = configs.timezone) {
    return moment_tz.tz(date, format, tz);
}

export function formatDateWithTimezone(date, tz = configs.timezone, format = '') {
    return moment_tz(date || new Date()).tz(tz).format(format);
}
