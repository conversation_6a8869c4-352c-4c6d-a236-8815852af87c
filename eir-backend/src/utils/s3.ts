import multer from 'multer';
import multerS3 from 'multer-s3';
import fs from 'fs';
import { generateUUID } from '@utils/string';
import mime from 'mime-types';
import { getSignedUrl } from '@aws-sdk/s3-request-presigner';

const {
    S3Client,
    CopyObjectCommand,
    HeadObjectCommand,
    PutObjectCommand,
    ListObjectsCommand,
    DeleteObjectCommand,
} = require('@aws-sdk/client-s3');
import { fromEnv } from "@aws-sdk/credential-providers"; // ES6 import
import { GetObjectCommand } from '@aws-sdk/client-s3';

export const uploadMemStorage = multer({
    storage: multer.memoryStorage(),
});

let s3Client;

export function getS3Client(force = false) {
    if (!s3Client || force) {
        s3Client = new S3Client({
            region: process.env.S3_REGION,
            credentials: fromEnv(),
        });
    }
    return s3Client;
}

export function defaultMetadataFn(req, file, cb) {
    cb(null, { fieldName: file.fieldname });
}

export function defaultKeyFn(req, file, cb) {
    cb(null, `avatar/${generateUUID()}.jpg`);
}

export function getMulterS3Middleware(
    options = {
        metadata: defaultMetadataFn,
        key: defaultKeyFn,
    },
) {
    const { metadata, key } = options;
    const config = {
        storage: multerS3({
            s3: getS3Client(),
            bucket: process.env.S3_BUCKET,
            acl: 'public-read',
            metadata,
            key,
        }),
    };
    return multer(config);
}

export function getImageUrl(fileByPath) {
    return `https://${process.env.S3_BUCKET}.s3-${process.env.S3_REGION}.amazonaws.com/${fileByPath}`;
}

export async function getSignedUrlForS3(
    key: string,
    expireTime: number = 3600,
) {
    if (!key) return '';
    const params = {
        Bucket: process.env.S3_BUCKET,
        Key: key,
        Expires: expireTime,
    };
    return getSignedUrl(getS3Client(), new GetObjectCommand(params), {
        expiresIn: expireTime,
    });
}

export function moveFile(oldPath, newPath) {
    const params = new CopyObjectCommand({
        Bucket: process.env.S3_BUCKET,
        CopySource: `${process.env.S3_BUCKET}/${oldPath}`,
        Key: newPath,
        // ACL: 'public-read',
    });
    return getS3Client().send(params);
}

export function checkIfFileExists(filePath) {
    const params = new HeadObjectCommand({
        Bucket: process.env.S3_BUCKET,
        Key: filePath,
    });
    return getS3Client().send(params);
}

export function uploadXLSX({ key, filePath }) {
    return getS3Client().send(new PutObjectCommand({
        Bucket: process.env.S3_BUCKET,
        Key: key,
        Body: fs.createReadStream(filePath),
        ContentType: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        // ACL: 'public-read',
    }));
}

export async function uploadFileToS3(filepath: string, key: string = '', publicAcl: boolean = true): Promise<string> {
    const s3Client = getS3Client();

    await s3Client.send(
        new PutObjectCommand({
            Bucket: process.env.S3_BUCKET,
            Key: key,
            Body: fs.createReadStream(filepath),
            ContentType: mime.lookup(filepath),
            ACL: publicAcl ? 'public-read' : 'private',
        }),
    );

    return getImageUrl(key);
}

export async function deleteS3Folder(folder) {
    try {
        const s3Client = getS3Client();
        const Bucket = process.env.S3_BUCKET;
        const result = await s3Client.send(new ListObjectsCommand({ Bucket, Prefix: folder }));
        return await Promise.all(
            result.Contents.map((content) => s3Client.send(new DeleteObjectCommand({ Bucket, Key: content.Key }))));

    } catch (error) {
        console.error(error);
        return false;
    }
}

export async function uploadFileToS3FromStream({
    mimetype = 'image/png',
    filestream,
    key = generateUUID(),
    publicAcl = true,
}): Promise<string> {
    const s3Client = getS3Client();

    await s3Client.send(
        new PutObjectCommand({
            Bucket: process.env.S3_BUCKET,
            Key: key,
            Body: filestream,
            ContentType: mimetype,
            ACL: publicAcl ? 'public-read' : 'private',
        }),
    );

    return key;
}

let multerMiddleware;
export function getMulterMiddleware(): any {
    if (!multerMiddleware) {
        multerMiddleware = multer({
            dest: 'public/',
        });
    }
    return multerMiddleware;
}
