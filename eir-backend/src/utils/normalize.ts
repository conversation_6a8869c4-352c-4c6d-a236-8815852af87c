import models from '@models';

export function normalizeUser(user, metaData = {}) {
    return {
        id: user.id,
        username: user.username,
    };
}

export function normalizeAdmin(admin, data = {}) {
    return {
        id: admin.id,
        email: admin.email,
        username: admin.username,
        avatar: admin.avatar,
        name: admin.name,
        role: admin.role,
        active: admin.active,
        created_at: admin.created_at,
        deactivated_at: admin.deactivated_at,
        ...data,
    };
}
