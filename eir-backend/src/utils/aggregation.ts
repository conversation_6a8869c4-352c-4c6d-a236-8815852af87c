import mongoose, {
    AccumulatorOperator,
    AggregateOptions,
    Model,
    QuerySelector,
    RootQuerySelector,
    FilterQuery,
    PipelineStage,
    Expression,
} from 'mongoose';
import * as _ from 'lodash';
import { UnionToIntersection } from '@src/interfaces/utils';

type IntersectionPipelineStage = UnionToIntersection<PipelineStage>;

type DuyLeMatchStage<DocumentType> = FilterQuery<DocumentType & { [k: string]: any }> &
    QuerySelector<any> &
    RootQuerySelector<any>;

export default class Aggregation<ModelType, DocumentType> {
    private readonly stages: any[] = [];
    private readonly Collection: Model<DocumentType, any, any>;

    constructor(Collection?) {
        this.Collection = Collection;
    }

    /**
     * Create Aggregate instance without `new` keyword and implicit Type
     */
    static newPipeline<T>(Collection?: T) {
        if (!Collection) return new Aggregation<any, any>();
        type DocumentType = T extends Model<infer D, any, any> ? D : any;
        return new Aggregation<T, DocumentType>(Collection);
    }

    project(fields: Record<string, any>) {
        this.stages.push({ $project: fields });
        return this;
    }

    match(condition: DuyLeMatchStage<DocumentType> = {}) {
        this.stages.push({ $match: condition });
        return this;
    }

    /**
     * Stressed with `{ $match: { $expr: { $eq: [key1, key2] } } }`? Use `aggregation.matchField(key1, key2)`
     */
    matchField(key1: string, key2: string) {
        this.stages.push({ $match: { $expr: { $eq: [key1, key2] } } });
        return this;
    }

    sort(fields: Record<string, 1 | -1 | Expression.Meta>) {
        this.stages.push({ $sort: fields });
        return this;
    }

    /**
     * Unwind with default `preserveNullAndEmptyArrays = true`
     */
    unwind(path: string, options?: Omit<Exclude<PipelineStage.Unwind['$unwind'], string>, 'path'>) {
        const defaultOptions = { preserveNullAndEmptyArrays: true };
        options = _.merge(defaultOptions, options);
        this.stages.push({ $unwind: { path, ...options } });
        return this;
    }

    /**
     * Pass _id or id to set _id of $group stage (default: null). { $group: { _id: `_id ?? id ?? null`, ...otherFields } }
     * @example
     * ```js
     * Aggregation
     *  .newPipeline(UserScore)
     *  // { $group: { _id: "$user", maxScore: { $max: "$score" } } }
     *  .group({id: "$user", maxScore: {$max: "$score"}})
     *  // { $group: { _id: null, avgMaxScore: { $avg: "$maxScore" } } }
     *  .group({avgMaxScore: {$avg: "$maxScore"}})
     *  .run()
     * ```
     */
    group(groupStageOptions: { id?: any; _id?: any } | Record<string, AccumulatorOperator> = {}) {
        const { _id, id, ...fields } = groupStageOptions;
        this.stages.push({ $group: { _id: _id ?? id ?? null, ...fields } });
        return this;
    }

    /**
     * ```js
     * aggregate.groupCount({id: "$default_null", name: "default: 'total'" }).log()
     * // { $group: { _id: "$default_null", "default: 'total'": { $sum: 1 } } }
     * ```
     */
    groupCount(groupCountParam?: { id?: any; _id?: any; name?: string }) {
        const { _id, id, name } = groupCountParam;
        const param = {
            _id: _id ?? id ?? null,
            [name ?? 'total']: { $sum: 1 },
        };
        this.stages.push({
            $group: param,
        });
        return this;
    }

    /**
     * ```js
     * aggregate.groupSum({id: "$user", totalRecord: 1, totalQty: "$qty"}).log()
     * // { $group: { _id: "$user", total: { $sum: 1 }, totalQty: { $sum: "$qty" } } }
     * ```
     */
    groupSumBy(groupSumParam?: { id?: any; _id?: any; [k: string]: any }) {
        const { _id, id, ...fields } = groupSumParam;
        const param = {
            _id: _id ?? id ?? null,
            ..._.fromPairs(_.entries(fields).map(([k, v]) => [k, { $sum: v }])),
        };
        this.stages.push({
            $group: param,
        });
        return this;
    }

    lookup(lookupStageOptions?: {
        from: string | Model<any>;
        as: string;
        localField?: string;
        foreignField?: string;
        pipeline?: Exclude<PipelineStage, PipelineStage.Merge | PipelineStage.Out>[];
        let?: Record<string, any>;
        unwind?: boolean;
        [k: string]: any;
    }) {
        if (!lookupStageOptions) return this;
        const { from, as, foreignField, localField, pipeline, unwind, ...otherLet } = lookupStageOptions;
        if (typeof from !== 'string') {
            lookupStageOptions.from = mongoose.pluralize()(from.modelName);
        }

        const param = _.omitBy(
            {
                from: lookupStageOptions.from,
                as,
                foreignField,
                localField,
                pipeline,
                let: _.merge(lookupStageOptions.let, otherLet),
            },
            _.isNil,
        );

        this.stages.push({
            $lookup: param,
        });
        if (unwind ?? true) return this.unwind('$' + as);

        return this;
    }

    /**
     * Use to add raw pipeline stage
     */
    rawStage<
        StageName extends keyof IntersectionPipelineStage,
        StageParams extends IntersectionPipelineStage[StageName],
    >(stageName: StageName, stageParams: StageParams) {
        this.stages.push({ [stageName]: stageParams });
        return this;
    }

    log() {
        console.log(JSON.stringify(this.stages, (key, value) => (value === Infinity ? 'Infinity' : value), 4));
    }

    /**
     * Return current pipeline's stages
     */
    pipeline() {
        return [...this.stages];
    }

    run(options?: AggregateOptions) {
        return this.Collection.aggregate(this.stages, options);
    }

    explain(options?: AggregateOptions) {
        return this.Collection.aggregate(this.stages, options).explain();
    }

    static dateToString(
        field = '$created_at',
        format = Aggregation.DATE_FORMAT.ISO_8601,
        timezone = Aggregation.TIMEZONE.SINGAPORE,
    ) {
        return {
            $dateToString: {
                date: field,
                timezone: timezone ?? '+08',
                format,
            },
        };
    }

    static readonly TIMEZONE = {
        SINGAPORE: '+08',
        HO_CHI_MINH: '+07',
    };

    static readonly DATE_FORMAT = {
        DATE: '%Y-%m-%d',
        TIME: '%H:%M:%S',
        ISO_8601: '%Y-%m-%dT%H:%M:%S%z',
    };
}
