import XLSX, { ParsingOptions } from 'xlsx';
import fs from 'fs';
import { getMoment } from '@utils/datetime';
import * as _ from 'lodash';

const XLSX_POPULATE = require('xlsx-populate');
const csv = require('csv-parser');

/**
 *
 * @returns {*[]}
 * @param filePath
 * @param options
 */
export function readXLSX(filePath: string, options: ParsingOptions = {}, ): any[] {
    const wb = XLSX.readFile(filePath, options);
    const result = [];
    for (const sheetName of wb.SheetNames) {
        result.push(
            XLSX.utils.sheet_to_json<object>(wb.Sheets[sheetName]).map((obj) => {
                const temp = {};
                Object.entries(obj).map(([key, value]) => {
                    temp[_.snakeCase(_.trim(key))] = value;
                });
                return temp;
            }),
        );
    }
    return result;
}

/**
 *
 *
 * @param sheets sheets data [{name, data}]
 * @param filename String
 */
export function writeXLSX(sheets: { data: any[]; name: string; }[], filename: string) {
    console.log('sheets: ', sheets[0].name, filename);
    const wb = XLSX.utils.book_new();
    for (const sheet of sheets) {
        XLSX.utils.book_append_sheet(wb, XLSX.utils.json_to_sheet(sheet.data), sheet.name);
    }
    console.log('writing file : ', filename);
    return XLSX.writeFile(wb, filename);
}

const ec = (r: any, c: number) => {
    return XLSX.utils.encode_cell({ r: r, c: c });
};
const deleteWorksheetRow = (ws: { [x: string]: string; }, row_index: any) => {
    let range = XLSX.utils.decode_range(ws['!ref']);
    for (let R = row_index; R < range.e.r; ++R) {
        for (let C = range.s.c; C <= range.e.c; ++C) {
            ws[ec(R, C)] = ws[ec(R + 1, C)];
        }
    }
    range.e.r--;
    ws['!ref'] = XLSX.utils.encode_range(range.s, range.e);
};

function _writeXLSXWithPassword({ data = [{ data: [], name: 'Sheet1' }], password, filename, cb }) {
    XLSX_POPULATE.fromBlankAsync().then((wb) => {
        try {
            data.map((sheet) => {
                const ws = XLSX.utils.json_to_sheet(sheet.data);
                delete ws['!ref'];
                wb.addSheet(sheet.name);
                Object.keys(ws).map((cell) => {
                    wb.sheet(sheet.name).cell(cell).value(ws[cell].v);
                });
            });
            wb.deleteSheet('Sheet1');
            console.log('Writing excel file with password ', password);
            return wb
                .toFileAsync(filename || `${getMoment().format('DDMMYY')}.xlsx`, { password })
                .then(
                    cb ||
                    function () {
                        console.log('done');
                    },
                )
                .catch((err) => {
                    console.log('error: ');
                    (cb && cb(err)) || console.log(err);
                });
        } catch (err) {
            (cb && cb(err)) || console.log(err);
        }
    });
}

export async function writeXLSXWithPassword({ data = [{ data: [], name: 'Sheet1' }], password, filename, cb }) {
    return new Promise((resolve, reject) => {
        _writeXLSXWithPassword({
            data,
            password,
            filename,
            cb: cb
                ? cb
                : (err) => {
                    if (err) {
                        console.log(err);
                        return reject(err);
                    }
                    console.log(`Done ${filename} - ${password}`);
                    resolve(true);
                },
        });
    });
}

export function parseCSV(filePath, mapHeaderCb?, formatDataCb?) {
    const result = [];
    return new Promise((resolve) => {
        fs.createReadStream(filePath)
            .pipe(
                csv({
                    mapHeaders:
                        mapHeaderCb ||
                        function ({ header }) {
                            return header.trim().toItemUid();
                        },
                }),
            )
            .on('data', (data) => {
                result.push((formatDataCb && formatDataCb(data)) || data);
            })
            .on('end', () => {
                resolve(result);
            });
    });
}
