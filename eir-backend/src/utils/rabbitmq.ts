import client, { Connection, Channel, Options } from 'amqplib';

import Connect = Options.Connect;
import configs from '@config';

interface QueueOption {
    prefetch?: number;
    durable: boolean;
    maxPriority?: number;
    priority?: boolean;
    noAck?: boolean;
    persistent?: boolean;
}

let myQueue: MyRabbitMQ;

export async function getQueueConnection(): Promise<MyRabbitMQ> {
    if (myQueue) {
        return myQueue;
    }
    myQueue = new MyRabbitMQ(configs.amqpConnection);
    await myQueue.connect();
    console.log('🐇 connected', configs.amqpConnection);
    return myQueue;
}

export class MyRabbitMQ {
    public connection: Connection;
    private channel: Channel;
    private readonly config: string | Connect;

    constructor(config: string | Connect) {
        this.config = config;
    }

    async connect() {
        this.connection = await client.connect(this.config);
    }

    async configQueue(queueName: string, options: QueueOption = { durable: true }) {
        this.channel = await this.connection.createChannel();
        await this.channel.prefetch(options.prefetch || 1);
        await this.channel.assertQueue(queueName, options);
    }

    sendMessage(queue: string, msg: string, options: any = {}) {
        console.info(`sending to ${queue} a msg: ${msg}`);
        return this.channel.sendToQueue(queue, Buffer.from(msg), {
            persistent: true,
            ...options,
        });
    }

    registerSingleConsumer(queueName, callback: (...args) => Promise<Boolean>) {
        const channel = this.channel;
        return channel.consume(queueName, async function(message) {
            let msg;
            try {
                msg = JSON.parse(message.content.toString());
            } catch (error) {
                msg = message.content.toString();
            }
            const result = await callback(msg);
            console.log('result', result);
            if (result) channel.ack(message);
            else channel.nack(message);
        }, { noAck: false });
    }

}
