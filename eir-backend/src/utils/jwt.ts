import jwt from 'jsonwebtoken';
import config from '@config';

export function makeJWT(payload, privateKey = config.keys.user.private_key, options = {}) {
    try {
        // console.log('config.keys.user.private_key', config.keys.user.private_key);
        return jwt.sign(payload, privateKey, {
            issuer: 'https://sqkii.com',
            algorithm: 'RS256',
            ...options,
        });
    } catch (error) {
        console.log('makeJWT: ', error);
        return null;
    }
}

export function decodeJWT(token, publicKey = config.keys.user.public_key, options = {}) {
    try {
        return jwt.verify(token, publicKey);
    } catch (error) {
        return null;
    }
}
