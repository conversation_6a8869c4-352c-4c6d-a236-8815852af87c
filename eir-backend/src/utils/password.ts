const bcrypt = require('bcrypt');


/**
 * Hash password text using bcrypt algorithm.
 *
 * @param {string} password - unhashed password
 * @returns {Promise} containing hashed password
 */
export function hashPassword(password) {
    return bcrypt.hash(password, 10);
}

/**
 * Check if two passwords match.
 *
 * @param {string} password1
 * @param {string} password2
 * @returns {Promise} containing result of comparison
 */
export function checkPasswordsMatch(password1, password2) {
    return bcrypt.compare(password1, password2);
}


export function validatePassword(password) {
    const regexDigit = /\d/;
    const regexLowercase = /[a-z]/;
    const regexUppercase = /[A-Z]/;
    const regexSpecial = /[`~!@#$%^&*()\-_+=[{\]}\\|;:'",<.>/?]/;

    if (!(
        password.length >= 8
        && regexDigit.test(password)
        && regexLowercase.test(password)
        && regexUppercase.test(password)
        && regexSpecial.test(password)
        )
    ) {
        return [
            'Password requirements:',
            '\u2009\u2009\u2022\u2009 Must contain at least 8 characters',
            '\u2009\u2009\u2022\u2009 Must contain at least 1 lowercase (a-z)',
            '\u2009\u2009\u2009\u2009 Must contain at least 1 uppercase (A-Z)',
            '\u2009\u2009\u2009\u2009 Must contain at least one digit (0-9)',
            '\u2009\u2009\u2009\u2009 Must contain at least 1 special character',
        ].join('\n');
    }
    return '';
}
