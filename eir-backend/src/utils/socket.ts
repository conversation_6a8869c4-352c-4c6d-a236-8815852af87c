import { createAdapter } from '@socket.io/redis-adapter';
import configs from '@src/config';
import { SocketEvent, SocketResponse } from '@src/interfaces/socket';
import models from '@src/models';
import express from 'express';
import http from 'http';
import { Server } from 'socket.io';
import { registerEvent } from './event';
import { decodeJWT } from './jwt';
import { validateHeaders } from './lib';
import { newRedisClient } from './redis';
import { generateUUID } from './string';
import SOCKET_EVENT from '@constants/socket';

function applyBaseResponse(cb, responded): SocketResponse {
    const success = (data) => {
        try {
            cb({
                status: 'success',
                data,
                error_message: '',
                error_code: '',
            });
            responded = true;
        } catch (error) {}
    };
    const error = (error_code, error_message?: string, data?: any) => {
        try {
            cb({
                status: 'error',
                data: configs.app.isProd ? null : data ?? null,
                error_message: error_message ?? '',
                error_code: error_code ?? 0,
            });
            responded = true;
        } catch (error) {}
    };
    return {
        ok: (data) => success(data),
        badRequest: (errorMessage?: string, errorData?: any) => error(400, errorMessage ?? 'bad_request', errorData),
        rateLimited: (errorMessage?: string, errorData?: any) =>
            error(409, errorMessage ?? 'too_many_request', errorData),
        serverInternalError: (errorMessage?: string, errorData?: any) =>
            error(500, errorMessage ?? 'bad_request', errorData),
    };
}

export default class SocketServer {
    static io: Server;

    static async initSocket(server?: http.Server) {
        if (!server) {
            const app = express();
            server = http.createServer(app);
            app.listen(configs.app.port, () => {
                const printWithWrap = (s) => console.log(s.padStart(30 + s.length / 2, '=').padEnd(60, '='));
                printWithWrap(` ENV: ${configs.app.env} `);
                console.log('🚀 App listening on the port', 3000);
                printWithWrap('');
            });
        }
        const io = new Server(server, {
            pingTimeout: 5000,
            pingInterval: 5000,
            allowEIO3: true,
            cors: {
                origin: configs.app.ORIGIN,
                credentials: true,
            },
            maxHttpBufferSize: 1e8,
        });
        io.use(async (socket, next) => {
            let { token, role, ctime, sig } = socket.handshake.auth;
            role = role ?? 'user';
            try {
                const validHeader = validateHeaders({
                    headers: {
                        ctime,
                        sig,
                    },
                    path: '/socket',
                    method: 'get',
                });
                if (!validHeader && !configs.app.isDev) {
                    return next(new Error('Permission denied!'));
                }
                if (token.startsWith('Bearer ')) {
                    token = token.slice(7);
                }
                if (!token) return next(new Error('Invalid token!'));
                const decoded = (await decodeJWT(token, configs.keys[role].public_key)) as any;
                if (!decoded || !decoded.id) return next(new Error('Invalid token!'));
                const user = await models[role.capitalize()].findOne({ _id: decoded.id });
                if (!user || user.token !== token) return next(new Error('Unauthorized!'));
                socket.data = {
                    user_id: user.id,
                    session_id: generateUUID(),
                    start_time: Date.now(),
                };
                socket.join(user.id);
                socket.join(SOCKET_EVENT.ANNOUNCEMENT);
                socket.emit(SOCKET_EVENT.AUTHENTICATED, 'success');
                next();
            } catch (error) {
                console.log(error);
                return next(error);
            }
        });

        const [pubClient, subClient] = await Promise.all([newRedisClient(), newRedisClient()]);
        io.adapter(
            createAdapter(pubClient, subClient, {
                key: configs.session.name,
            }) as any,
        );
        SocketServer.io = io;
        registerEvent('socket-event', function (args: { channel: string; event: string; data: any }) {
            const { channel, event, data } = args;
            if (!channel) return SocketServer.sendToAll(event, data);
            else SocketServer.sendToChannel(channel, event, data);
        });
    }

    static bindHandlers({
        onSocketConnect,
        onSocketDisconnect,
        socketEvents,
    }: {
        onSocketConnect?: any;
        socketEvents?: SocketEvent[];
        onSocketDisconnect?: any;
    }) {
        SocketServer.io.on('connection', async (socket) => {
            await onSocketConnect?.(socket);
            for (const socketEvent of socketEvents || []) {
                socket.on(socketEvent.event, async (payload, cb) => {
                    let responded = false;
                    const req: any = { body: payload, socket };
                    const res = applyBaseResponse(cb, responded);
                    for (const middleware of socketEvent.middlewares || []) {
                        if (responded) return;
                        await new Promise((resolve) => {
                            middleware(req, res, resolve);
                        });
                    }
                    socketEvent.handler(req, res);
                });
            }
            onSocketDisconnect && socket.on('disconnect', async () => await onSocketDisconnect(socket));
        });
    }

    static sendToAll(event, data?) {
        if (!SocketServer.io) return;
        SocketServer.io.emit(event, data);
    }

    static sendToChannel(channel, event, data?) {
        if (!SocketServer.io) return;
        SocketServer.io.to(channel.toString()).emit(event, data);
    }

    static async joinChannel(userId: any, channel: any) {
        userId = userId?.toString();
        channel = channel?.toString();
        if (!userId || !channel || !SocketServer.io) return false;
        const sockets = await SocketServer.io.sockets.in(userId).fetchSockets();
        for (const socket of sockets) {
            socket.join(channel);
        }
    }
}
