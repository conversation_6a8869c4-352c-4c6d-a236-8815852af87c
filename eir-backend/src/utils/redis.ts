import * as redis from 'redis';
import configs from '@config';
import { generateRedisKey, generateSingleKey } from '@utils/string';
import { getMoment } from '@utils/datetime';

function getNumOfSecondsTilEndOfDay() {
    return Math.ceil(getMoment().endOf('day').diff(getMoment()) / 1000);
}

class RedisClient {
    public client: redis.RedisClientType & redis.RedisDefaultModules;

    constructor(client: any) {
        this.client = client;
    }

    async generateLeaderboardKey(key = 'lb_key') {
        const check = await this.client.incr(key);
        return 9999999999 - check + '';
    }

    expireKey(type: string, user_id: string, second = 0) {
        if (!second) second = getNumOfSecondsTilEndOfDay();
        return this.client.expire(generateRedisKey(type, user_id), second);
    }

    getKey(type: string, user_id: string) {
        return this.client.get(generateRedisKey(type, user_id));
    }

    getExpireTime(type: string, user_id: string) {
        return this.client.ttl(generateRedisKey(type, user_id));
    }

    setKey(type: string, user_id: string, value: string, expire?: number) {
        return this.client.set(generateRedisKey(type, user_id), value, +expire && { EX: expire });
    }

    increaseUserKey(type: string, user_id: string, quantity = 1) {
        return this.client.incrBy(generateRedisKey(type, user_id), quantity);
    }

    increaseKey(type: string, quantity = 1) {
        return this.client.incrBy(generateSingleKey(type), quantity);
    }

    decreaseKey(type: string, user_id: string, quantity = 1) {
        return this.client.decrBy(generateRedisKey(type, user_id), quantity);
    }

    getList(type: string) {
        return this.client.sMembers(generateSingleKey(type));
    }

    addToList(type: string, data: string) {
        return this.client.sAdd(generateSingleKey(type), data);
    }

    addManyToList(type: string, data: string[] = []) {
        return this.client.sAdd(generateSingleKey(type), data);
    }

    removeFromList(type: string, data: string) {
        return this.client.sRem(generateSingleKey(type), data);
    }

    removeManyFromList(type: string, data: string) {
        return this.client.sRem(generateSingleKey(type), data);
    }

    getListSize(type: string) {
        return this.client.sCard(generateSingleKey(type));
    }

    isInList(type: string, data: string) {
        return this.client.sIsMember(generateSingleKey(type), data);
    }

    deleteKey(type: string, user_id: string) {
        return this.client.del(generateRedisKey(type, user_id));
    }

    deleteSingleKey(type: string) {
        return this.client.del(generateSingleKey(type));
    }

    async isScheduler(type: string, expire = 0) {
        const key = generateRedisKey('scheduler', type);
        const check = await this.client.incr(key);
        if (check > 1) return null;
        await this.client.expire(key, expire || 60);
        return key;
    }

    async raceCondition(type: string, expire = 60) {
        const key = generateSingleKey('RACE_CONDITION_' + type);
        const isKeySet = await this.client.setNX(key, '1');
        if (!isKeySet) return false;
        await this.client.expire(key, expire || 60);
        return key;
    }

    async getKeysByPattern(pattern: string) {
        pattern = generateSingleKey(pattern);
        const keys = [];
        for await (const key of this.client.scanIterator({ MATCH: pattern, COUNT: 500 })) {
            keys.push(key);
        }
        return keys;
    }

    async getValuesByPattern(pattern: string) {
        const keys = await this.getKeysByPattern(pattern);
        return Promise.all(keys.map((k) => this.client.get(k)));
    }

    async getPairsByPattern(pattern: string) {
        const keys = await this.getKeysByPattern(pattern);
        return Promise.all(keys.map(async (k) => [k, await this.client.get(k)]));
    }
}

let client: redis.RedisClientType & redis.RedisDefaultModules;
let writerInstance: RedisClient, readerInstance: RedisClient;

export function getRedisClient() {
    if (!writerInstance) {
        return (writerInstance = new RedisClient(client));
    }
    return writerInstance;
}

let subscriber: any;
let publisher: any;

export async function getSubscriber() {
    if (!subscriber) {
        subscriber = await newRedisClient();
    }
    return subscriber;
}

export async function getPublisher() {
    if (!publisher) {
        publisher = await newRedisClient();
    }
    return publisher;
}

export async function publish(event: string, message: string) {
    return (await getPublisher()).publish(event, message);
}

export async function newRedisClient(config = configs.redis): Promise<any> {
    return new Promise(async (resolve) => {
        try {
            const client = redis.createClient(config);
            await client.connect();
            console.log('🥡 Redis connected', config);
            return resolve(client);
        } catch (error) {
            console.log('error', error, config);
            await new Promise((resolve) => setTimeout(resolve, 3000));
            return resolve(newRedisClient(config));
        }
    });
}

export function setClient(redisClient: any) {
    client = redisClient;
}

