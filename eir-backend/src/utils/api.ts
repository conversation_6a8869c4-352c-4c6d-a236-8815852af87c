import axios, {
    AxiosError,
    AxiosInstance,
    AxiosRequestConfig,
    type AxiosResponse,
} from 'axios';
import configs from '@config';
import { getRedisClient } from './redis';

function handleError(error: any) {
    if (error instanceof AxiosError) {
        console.log('❌ Axios post err', {
            status: error.response?.status,
            statusText: error.response?.statusText,
        });
        return {
            status: error.response?.status,
            statusText: error.response?.statusText,
            data: error.response?.data,
        };
    }
    return error;
}

export class API {
    private readonly client: AxiosInstance;

    constructor(config: AxiosRequestConfig) {
        this.client = axios.create(config);
        this.client.interceptors.request.use(
            function (config) {
                config.timeout = 30000;
                return config;
            },
            function (error) {
                console.log('❌ request interceptors', error);
                return Promise.reject(error);
            },
        );
    }

    post(
        path: string,
        data: any,
        config?: AxiosRequestConfig,
    ): Promise<AxiosResponse['data']> {
        return new Promise(async (resolve, reject) => {
            try {
                const res = await this.client.post(path, data, config);
                return resolve(res.data);
            } catch (error) {
                return reject(handleError(error));
            }
        });
    }

    get(path: string, config: AxiosRequestConfig) {
        return new Promise(async (resolve, reject) => {
            try {
                const res = await this.client.get(path, config);
                return resolve(res.data);
            } catch (error) {
                return reject(handleError(error));
            }
        });
    }
}

export const CTRClient = {
    client: new API(configs.clients.ctr.config),
    verifyToken: async function (token: string) {
        console.log(token);
        try {
            return await this.client.get(`/validate-token`, {
                headers: {
                    Authorization: `${token}`,
                },
            });
        } catch (error) {
            console.error('❌[CTRClient] verifyToken', error);
        }
    },
    getToken: async function () {
        try {
            const response = await this.client.post(`/authentication`, {
                strategy: 'local',
                email: process.env.CDAS_USERNAME,
                password: process.env.CDAS_PASSWORD,
                device: {
                    os: 'computer',
                    FCMId: 'portal',
                },
                remember: true,
            });
            return response.accessToken;
        } catch (error) {
            console.error('❌[CTRClient] getToken', error);
        }
    },
    sendEmail: async function ({
        emails,
        subject,
        message,
        attachments,
    }: {
        emails: string[];
        subject: string;
        message: string;
        attachments?: {
            filename: string;
            content: string;
            encoding: string;
            contentType: string;
        };
    }) {
        try {
            const token = await this.getToken();
            const response = await this.client.post(
                '/custom-notification',
                {
                    from:
                        process.env.NODE_ENV === 'production'
                            ? '<EMAIL>'
                            : '<EMAIL>',
                    receiver: emails.join(','),
                    type: 'email',
                    attachments: attachments?.content || 'dGhpcyBpcyB0aGFuZw==',
                    filename: attachments?.filename || 'filename',
                    subject,
                    html: `<p>${message}</p>`,
                    description: `${message}`,
                },
                {
                    headers: {
                        Authorization: token,
                        'Content-Type': 'application/json',
                    },
                },
            );
            return response;
        } catch (error) {
            console.error('❌[CTRClient] sendEmail', error);
            throw error;
        }
    },
};

export const CTRClientUAT = {
    client: new API(configs.clients.ctr_uat.config),
    getToken: async function () {
        try {
            const response = await this.client.post(`/authentication`, {
                strategy: 'local',
                email: process.env.CDAS_UAT_USERNAME,
                password: process.env.CDAS_UAT_PASSWORD,
                device: {
                    os: 'computer',
                    FCMId: 'portal',
                },
                remember: true,
            });
            return response.accessToken;
        } catch (error) {
            console.error('❌[CTRClientUAT] getToken', error);
        }
    },

    sendEmail: async function ({
        emails,
        subject,
        message,
        attachments,
    }: {
        emails: string[];
        subject: string;
        message: string;
        attachments?: {
            filename: string;
            content: string;
            encoding: string;
            contentType: string;
        };
    }) {
        try {
            const token = await this.getToken();
            console.log({ content: attachments.content });
            console.log({ filename: attachments.filename });
            const response = await this.client.post(
                '/custom-notification',
                {
                    from:
                        process.env.NODE_ENV === 'production'
                            ? '<EMAIL>'
                            : '<EMAIL>',
                    receiver: emails.join(','),
                    subject,
                    type: 'email',
                    description: 'email',
                    filename: attachments?.filename || 'filename',
                    html: `<p>${message}</p>`,
                    attachments:
                        attachments?.content.toString() ||
                        'dGhpcyBpcyB0aGFuZw==',
                },
                {
                    headers: {
                        Authorization: token,
                    },
                },
            );
            return response;
        } catch (error) {
            console.error('❌[CTRClientUAT] sendEmail', error);
            throw error;
        }
    },
};

export const RoadNameClient = {
    client: new API(configs.clients.roadname.config),
    getRoadName: async function (latitude: number, longitude: number) {
        try {
            const token = await (process.env.NODE_ENV !== 'production'
                ? CTRClientUAT.getToken()
                : CTRClient.getToken());
            console.log('LOG-token', token);
            const response = await this.client.post(
                'road-map/name',
                {
                    latitude: `${latitude}`,
                    longitude: `${longitude}`,
                },
                {
                    headers: {
                        Authorization: token,
                        'Content-Type': 'application/json',
                    },
                },
            );
            console.log('LOG-response.name', response.name);
            return response.name;
        } catch (error) {
            console.error('❌[RoadNameClient] getRoadName', error);
        }
    },
};

export const EntityClient = {
    client: new API(configs.clients.entity.config),
    getToken: async function () {
        try {
            // const response = await this.client.post(`/authorizeServer/oauth/token?grant_type=client_credentials`, {
            //     username: process.env.BIZFILE_USERNAME,
            //     password: process.env.BIZFILE_PASSWORD,
            // });
            return 'qvOFt_z-rta-wfXVHONv1anHMHcL5RuKFjm9OqvUm19y1w5qHND88sWpYUVUaqcy0x2Jkk-9FQv7NJYG27EGB860LxsWVA06V1EmQHD9J5gE9QHQ8HYzgbxHqG7hgBSP';
        } catch (error) {
            console.error('❌[EntityClient] getToken', error);
        }
    },
    getDetail: async function (uen: string) {
        try {
            return await this.client.get('/entityBasicInformation', {
                params: {
                    uen,
                },
                headers: {
                    token: await this.getToken(),
                },
            });
        } catch (error) {
            console.error('❌[EntityClient] get entity detail', error);
        }
    },
};

export const OneMapClient = {
    client: new API(configs.clients.onemap.config),
    
    async getToken(): Promise<string> {
        try {
            // Check cache first
            const cachedToken = await getRedisClient().client.get('onemap_token');
            if (cachedToken) return cachedToken;
            
            // Get new token if not in cache
            const response = await this.client.post(`/api/auth/post/getToken`, {
                email: process.env.ONEMAP_USERNAME,
                password: process.env.ONEMAP_PASSWORD,
            });
            
            const token = response.access_token;
            // Cache token
            await getRedisClient().client.set('onemap_token', token, { EX: 60 });
            return token;
        } catch (error) {
            console.error('❌[OneMapClient] getToken', error);
            throw error;
        }
    },
    
    async getRoadName(
        latitude: number, 
        longitude: number, 
        returnObject = false
    ): Promise<string | [string, any]> {
        try {
            const token = await this.getToken();
            const location = `${latitude.toString()},${longitude.toString()}`;
            
            const response = await this.client.get(
                `/api/public/revgeocode?location=${encodeURIComponent(location)}&buffer=50&addressType=All&otherFeatures=N`,
                {
                    headers: {
                        Authorization: `Bearer ${token}`,
                    },
                },
            );
            
            if (response.GeocodeInfo?.length) {
                const info = response.GeocodeInfo[0];
                const roadName = [
                    info.BLOCK || '',
                    info.ROAD || '',
                    info.POSTALCODE ? info.POSTALCODE : ''
                ].filter(Boolean).join(' ').trim();
                
                return returnObject ? [roadName, info] : roadName;
            }
            
            const defaultResult = 'Invalid SG address';
            return returnObject ? [defaultResult, null] : defaultResult;
        } catch (error) {
            console.error('❌[OneMapClient] getRoadname', error);
            const defaultResult = 'Invalid SG address';
            return returnObject ? [defaultResult, null] : defaultResult;
        }
    },
};
