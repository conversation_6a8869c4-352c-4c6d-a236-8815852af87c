import * as _ from 'lodash';

interface Probability {
    [key: string]: number;
}

export function randomSingleSuccess(prob: Probability | Array<[any, number]>) {
    const entries = Array.isArray(prob) ? prob : _.entries(prob);
    for (const [value, chance] of entries) {
        if (Math.random() < chance) return value;
    }
    return entries.slice(-1)?.[0];
}

export function randomFromProb(prob: Probability) {
    const arr = [];
    Object.entries(prob).map(([value, chance]) => {
        const floor = arr.slice(-1)[0]?.ceil || 0;
        const ceil = floor + chance;
        arr.push({ value, floor, ceil });
    });
    const rand = _.random(arr[0].floor, arr.slice(-1)[0].ceil, true);
    return arr.find((item) => {
        return item.floor < rand && rand <= item.ceil;
    }).value;
}
