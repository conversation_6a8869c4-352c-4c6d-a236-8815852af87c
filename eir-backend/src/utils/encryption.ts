// eslint-disable-next-line
// @ts-nocheck
import crypto from 'crypto';
import config from '@config';
import { Schema } from 'mongoose';
import logger from './logger';
import { isEmpty, isNaN } from 'lodash';

declare type schemaType =
    | 'string'
    | 'undefined'
    | 'symbol'
    | 'number'
    | 'null'
    | 'array'
    | 'object'
    | 'boolean'
    | 'date'
    | 'function'
    | 'unknown';

const KEY = config.encryption.secret;
const IV = config.encryption.iv;

export const encrypt = (value: string): string => {
    try {
        if (isEmpty(value)) return value;
        if (!value || value === 'null' || value === 'undefined') return '';

        const cipher = crypto.createCipheriv('aes-256-cbc', KEY, IV);
        let encrypted = cipher.update(value, 'utf8', 'base64');
        encrypted += cipher.final('base64');
        return encrypted;
    } catch (error) {
        console.error(`🚫 encrypt | ${error}`);
        console.log('🚫 encrypt', error);
        return value;
    }
};

export const decrypt = (encrypted: string) => {
    try {
        if (isEmpty(encrypted)) return encrypted;
        if (!encrypted || encrypted === 'null' || encrypted === 'undefined') return '';

        const decipher = crypto.createDecipheriv('aes-256-cbc', KEY, IV);
        let decrypted = decipher.update(encrypted, 'base64', 'utf8');
        decrypted += decipher.final('utf8');
        return decrypted;
    } catch (error) {
        console.error(`🚫 decrypt | ${error}`);
        console.log('🚫 decrypt', error);
        return encrypted;
    }
};

export const encryptWithCredentials = (value, key, iv) => {
    try {
        if (isEmpty(value)) return value;
        if (!value || value === 'null' || value === 'undefined') return '';

        const cipher = crypto.createCipheriv('aes-256-cbc', key, iv);
        let encrypted = cipher.update(value, 'utf8', 'base64');
        encrypted += cipher.final('base64');
        return encrypted;
    } catch (error) {
        console.error(`🚫 encryptWithCredentials | ${error}`);
        return value;
    }
};

export const decryptWithCredentials = (encrypted, key, iv) => {
    try {
        if (isEmpty(encrypted)) return encrypted;
        if (!encrypted || encrypted === 'null' || encrypted === 'undefined') return '';

        const decipher = crypto.createDecipheriv('aes-256-cbc', key, iv);
        const decrypted = decipher.update(encrypted, 'base64', 'utf8');
        return decrypted + decipher.final('utf8');
    } catch (error) {
        console.error(`🚫 decryptWithCredentials | ${error}`);
        return encrypted;
    }
};

export const typeOf = (value: unknown): schemaType => {
    const str: string = Object.prototype.toString.call(value);
    const type = str.slice(8, -1).toLowerCase() as schemaType;

    switch (type) {
        case 'string':
        case 'undefined':
        case 'symbol':
        case 'number':
        case 'null':
        case 'array':
        case 'object':
        case 'boolean':
        case 'date':
        case 'function':
            return type;
        default:
            return 'unknown';
    }
};

function wrapWithType(value, type) {
    return `${type}|${value}`;
}

function unwrapTypeValue(value) {
    if (!value.includes('|')) return { value };
    const [type, ...rest] = value.split('|');
    if (!['number', 'string', 'boolean', 'date'].includes(type)) return { value };
    return {
        type,
        value: rest.join(''),
    };
}

export function sanitizeEncryptData(value, ok?) {
    if (ok) console.log('typeOf(value)', typeOf(value));
    const type = typeOf(value);
    switch (type) {
        case 'array':
            return value.map(sanitizeEncryptData);
        case 'object': {
            const keys = Object.keys(value);
            return Object.fromEntries(keys.map((k) => [k, sanitizeEncryptData(value[k])]));
        }
        case 'undefined':
        case 'null':
            return value;
        case 'date':
            return encrypt(wrapWithType(value.toISOString(), type));
        case 'number':
        case 'string':
            return encrypt(wrapWithType(value, type));
        default:
            return encrypt(wrapWithType(String(value), type));
    }
}

export function sanitizeDecryptData(value) {
    switch (typeOf(value)) {
        case 'object': {
            const keys = Object.keys(value);
            return Object.fromEntries(keys.map((k) => [k, sanitizeDecryptData(value[k])]));
        }
        case 'array':
            return value.map((item) => sanitizeDecryptData(item));
        default:
            return parseDataType(decrypt(value));
    }
}

export function parseDataType(data) {
    if (!data) return data;
    const { type, value } = unwrapTypeValue(data);
    switch (type) {
        case 'number':
            return Number(value);
        case 'string':
            return value;
        case 'boolean':
            return value === 'true';
        case 'date':
            return new Date(value);
        default:
            return value;
    }
}

export function configEncryption(schema: Schema, fields: Array<string>) {
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    const pattern: any = ['find', 'findOne', 'countDocuments', 'exists'];
    schema.pre(pattern, function () {
        fields.forEach((field) => {
            if (field in this._conditions) {
                this._conditions[field] = sanitizeEncryptData(this._conditions[field]);
            }
        });
    });

    schema.pre('save', function () {
        fields.forEach((field) => {
            if (field in this) {
                this[field] = sanitizeEncryptData(this[field]);
            }
        });
    });

    schema.pre(['updateOne', 'updateMany', 'findOneAndUpdate'], function () {
        fields.forEach((field) => {
            if (field in this._conditions) {
                this._conditions[field] = sanitizeEncryptData(this._conditions[field]);
            }
            if (field in this._update['$set']) {
                this._update['$set'][field] = sanitizeEncryptData(this._update['$set'][field]);
            }
            if (field in this._update) {
                this._update[field] = sanitizeEncryptData(this._update['$set'][field]);
            }
            if (field in this) {
                this[field] = sanitizeEncryptData(this[field]);
            }
        });
    });

    schema.post('save', function () {
        fields.forEach((field) => {
            if (this && this[field]) {
                this[field] = sanitizeDecryptData(this[field]);
            }
        });
    });

    function decryptFields(data) {
        fields.forEach((field) => {
            if (data && data[field]) {
                data[field] = sanitizeDecryptData(data[field]);
            }
        });
    }

    schema.post('findOne', decryptFields);
    schema.post('findById', decryptFields);
    schema.post(['find'], (result) => result.map(decryptFields));
}
