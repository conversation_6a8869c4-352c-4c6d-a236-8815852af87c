import './string';
import { createHmac, createHash } from 'crypto';
import { Document } from 'mongoose';
import { Route } from '@src/interfaces/routes';
import FileDownloader from 'nodejs-file-downloader';
import fs from 'fs';
import * as _ from 'lodash';
import { readFile } from 'fs/promises';
import { formatDateWithTimezone, ISO_DATE_REGEX } from '@utils/datetime';
import console from 'console';

function formatDate(data) {
    const stringData = JSON.stringify(data);
    return JSON.parse(
        stringData.replace(ISO_DATE_REGEX, (match) => {
            return formatDateWithTimezone(match);
        }),
    );
}

export function generateCredentialChecksum(req) {
    const h = createHash('sha256').update(JSON.stringify(req.body)).digest('base64');
    const stringToSign = `${process.env.CREDENTIAL_CLIENT_ID}
${h}
${req.headers.checksum}`;
    return createHmac('sha256', process.env.CREDENTIAL_CLIEND_SECRET).update(stringToSign).digest('base64');
}

export function validateHeaders(req) {
    try {
        if (req.path === '/webhook') return true;
        if (Date.now() - (+req.headers['ctime'] || 0) > 30000) return false;
        const stringToSign = `${req.method.toUpperCase()}
application/json
${req.headers['ctime']}
${req.path}
`;
        console.log('INFO | validateHeaders:', req.path);
        const hmac_signature = createHmac('sha256', process.env.HMAC_SECRET).update(stringToSign).digest('base64');
        return req.headers.sig === hmac_signature;
    } catch (error) {
        console.log('ERROR | validateHeaders', error);
        return false;
    }
}

export function isValidClientRequest({ path, time, sig, client, body }) {
    console.table({ time, sig, body });
    console.log('path: ', path);
    const diff = Date.now() - +time;
    const validTime = 0 <= diff && diff <= 30000;
    const h = createHash('sha256').update(body).digest('base64');
    if (!validTime) return false;
    const stringToSign = `POST
application/json
${time}
${path}
${h}
`;
    console.log('stringToSign: ', stringToSign);
    const hmac_signature = createHmac('sha256', client.secret).update(stringToSign).digest('base64');
    return sig === hmac_signature;
}

export interface CrudObj {
    list?: Partial<Route>;
    retrieve?: Partial<Route>;
    update?: Partial<Route>;
    create?: Partial<Route>;
    delete?: Partial<Route>;
    custom?: Partial<Route>[];
}

export class Crud {
    public model;
    public pathName;

    constructor(model: Document) {
        this.model = model;
        this.pathName = this.model.modelName.toLowerCase();
    }

    async list(req, res) {
        try {
            const { where, sort_by, sort_type, fields } = req.query;
            let conditions = {},
                filterFields = {};
            if (where) {
                where.split(',').map((k) => (conditions[k.split('=')[0]] = k.split('=')[1]));
            }
            if (fields) {
                fields.split(',').map((key) => (filterFields[key] = 1));
            }
            const sort = {};
            sort[sort_by || 'created_at'] = +sort_type || 1;
            var listData = await this.model.find(conditions, filterFields).sort(sort);
        } catch (error) {
            return res.serverInternalError(error.message);
        }
        return res.ok({
            total: listData.length,
            data: listData.map((item) => {
                return formatDate(item);
            }),
        });
    }

    async retrieve(req, res) {
        const { id } = req.params;
        try {
            var data = await this.model.findById(id);
        } catch (error) {
            return res.serverInternalError(error.message);
        }
        if (!data) {
            return res.badRequest(`${this.model.modelName} was not found or has been deleted!`);
        }
        return res.ok(formatDate(data));
    }

    async update(req, res) {
        const { id } = req.params;
        try {
            var data = await this.model.findById(id);
        } catch (error) {
            return res.serverInternalError(error.message);
        }
        if (!data) {
            return res.badRequest(`${this.model.modelName} was not found or has been deleted!`);
        }
        Object.keys(req.body).map((key) => {
            key in data && (data[key] = req.body[key]);
        });
        await data.save();

        return res.ok(formatDate(data));
    }

    async create(req, res) {
        try {
            const newData = await this.model.create(req.body);
            return res.ok(formatDate(newData));
        } catch (error) {
            console.log('===error=== ', error);
            return res.serverInternalError(error.message);
        }
    }

    async deleteData(req, res) {
        const { id } = req.params;
        try {
            var data = await this.model.findById(id);
        } catch (error) {
            return res.serverInternalError(error.message);
        }
        if (!data) {
            return res.badRequest(`${this.model.modelName} was not found or has been deleted!`);
        }
        await data.remove();

        return res.ok(formatDate(data));
    }

    generateCRUDObject(): CrudObj {
        return {
            list: {
                method: 'get',
                middleware: [],
                path: `/${this.pathName}`,
                cb: this.list.bind(this),
            },
            retrieve: {
                method: 'get',
                middleware: [],
                path: `/${this.pathName}/:id`,
                cb: this.retrieve.bind(this),
            },
            update: {
                method: 'put',
                middleware: [],
                path: `/${this.pathName}/:id`,
                cb: this.update.bind(this),
            },
            create: {
                method: 'post',
                middleware: [],
                path: `/${this.pathName}`,
                cb: this.create.bind(this),
            },
            delete: {
                method: 'delete',
                middleware: [],
                path: `/${this.pathName}/:id`,
                cb: this.deleteData.bind(this),
            },
        };
    }
}

export function createCrudAPIs(model: any & Document, config: CrudObj) {
    const newCRUD = new Crud(model);
    const crudObject = newCRUD.generateCRUDObject();
    const crud: any = {};
    const cruds = Object.values(crud);
    if (config.custom) {
        config.custom.map((x) => {
            x.path = `/${model.modelName.toLowerCase()}${x.path}`;
            cruds.push(x);
        });
    }
    ['list', 'retrieve', 'update', 'create', 'delete'].map((key) => {
        if (key in config)
            crud[key] = {
                ...crudObject[key],
                ...config[key],
            };
    });
    return cruds;
}

export const wait = (ms) => new Promise((resolve) => setTimeout(resolve, ms));

// downloadFile({url: "https://google.com/hehe.png", })
export async function downloadFile({
    url,
    destination,
    fileName,
    retry = 3,
    removeExt = false,
    headers = {},
}: {
    url: string;
    destination: string;
    fileName?: string;
    retry?: number;
    removeExt: boolean;
    headers?: any;
}): Promise<{ path: string; filename: string }> {
    const downloader = new FileDownloader({
        url,
        directory: destination,
        headers,
    });
    try {
        const { filePath, downloadStatus } = await downloader.download();
        let file = filePath;
        if (downloadStatus === 'COMPLETE' && fileName) {
            const newName = filePath.replace(/^(.*(?:\/|\\)).*(\.\w+)$/, `$1${fileName}${removeExt ? '' : '$2'}`);
            fs.renameSync(filePath, newName);
            file = newName;
        }
        const fileStat = fs.statSync(file);
        console.log(`Downloaded ${file} - ${_.round(fileStat.size / 1024, 2)}Kb`);
        return { path: file, filename: file.replace(/.*(?:\/|\\)(.*\.\w+)$/, '$1') };
    } catch (error) {
        if (retry < 0) {
            console.log(fileName, error);
            return { path: null, filename: null };
        } else
            return downloadFile({
                url,
                destination,
                fileName,
                retry: retry - 1,
                removeExt,
            });
    }
}

export async function readJsonFile(path) {
    const file = await readFile(path, 'utf8');
    return JSON.parse(file);
}


async function processChainPromise(promise, arrayPromises, result = []) {
    try {
        const data = await promise();
        result.push(data);
    } catch (error) {
        console.error(error);
    } finally {
        const promise = arrayPromises.pop();
        if (promise) {
            await processChainPromise(promise, arrayPromises, result);
        }
    }
}

export async function queuePromise(array, maxItem = 10) {
    const chunks = array.splice(0, maxItem);
    const result = [];
    await Promise.all(chunks.map(item => processChainPromise(item, array, result)));
    return result;
}
