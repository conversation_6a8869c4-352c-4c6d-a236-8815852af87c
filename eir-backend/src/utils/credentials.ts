import { createHash, createHmac } from 'crypto';

const axios = require('axios');
// TODO move to .env
const client_id = process.env.CREDENTIAL_CLIENT_ID;
const client_secret = process.env.CREDENTIAL_CLIENT_SECRET;

export const CREDENTIALS = {
    aws: {
        API_KEY: '',
        API_SECRET: '',
    },
    mailgun: {
        apiKey: '',
        domain: '',
    },
    unimatrix: {
        accessKeyId: '',
        accessKeySecret: '',
    },
};

const CREDENTIAL_URL = process.env.CREDENTIAL_CLIEND_URL;

export async function fetchCredentials(credential_types = Object.keys(CREDENTIALS)) {
    const body = {
        credential_types,
    };
    const headers = {
        'Content-Type': 'application/json',
        'Ctime': Date.now(),
    };
    const h = createHash('sha256').
        update(JSON.stringify(body)).
        digest('base64');
    const stringToSign = `POST
${headers['Content-Type']}
${headers['Ctime']}
/client/sqkii/credentials
${h}
`;
    const hmac_signature = createHmac('sha256', client_secret).
        update(stringToSign).
        digest('base64');
    headers['Authorization'] = `${client_id}:${hmac_signature}`;
    try {
        const res = await axios.post(CREDENTIAL_URL, body, {
            headers,
        });
        if (res.data.status === 'SUCCESS') {
            for (const c of res.data.data) {
                console.info('✅ credential updated', c.type);
                CREDENTIALS[c.type] = c.data;
                if (c.type === 'aws') {
                    process.env.AWS_ACCESS_KEY_ID = CREDENTIALS.aws.API_KEY;
                    process.env.AWS_SECRET_ACCESS_KEY = CREDENTIALS.aws.API_SECRET;
                }
            }
        } else {
            console.error('cant fetch credentials!');
        }

    } catch (error) {
        console.error(error.message);
        console.error('cant fetch credentials!');
    }
}
