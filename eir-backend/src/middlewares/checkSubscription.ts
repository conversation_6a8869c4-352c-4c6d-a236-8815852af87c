import { models } from "mongoose";

export async function checkSubscription(req, res, next) {
	try {
		const { user } = req;
		const subscription = await models.Subscription.findOne({
			company: user.company,
			start_date: { $lte: new Date() },
			end_date: { $gte: new Date() }
		});
		if (!subscription) {
			return res.forbidden('Subscription not found');
		}
		return next();
	} catch (error) {
		return res.serverInternalError(error.message);
	}
}