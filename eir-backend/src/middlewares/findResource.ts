import { getRedisClient } from '@utils/redis';

import models from '@models';

export function findResource({ modelName, fieldToGet, fieldToSearch, name, msg, data }) {
    fieldToGet = fieldToGet || 'id';
    fieldToSearch = fieldToSearch || '_id';
    name = name || 'resource';
    return async (req, res, next) => {
        try {
            if (!modelName || !models[modelName]) {
                throw Error('not_found');
            }
            const param = req.params[fieldToGet] || req.body[fieldToGet] || req.query[fieldToGet];
            if (!param) {
                throw Error('bad_request');
            }
            let resource;
            if (fieldToSearch === '_id') {
                resource = await models[modelName].findById(param);
            } else {
                resource = await models[modelName].findOne({ [fieldToSearch]: param });
            }
            if (!resource) throw Error('not_found');
            req[name] = resource;
            return next();
        } catch (e) {
            req.key && (await getRedisClient().client.del(req.key));
            switch (e.message) {
                case 'bad_request':
                case 'not_found':
                    return res.badRequest(msg, data);
                default:
                    return res.serverInternalError(e.message);
            }
        }
    };
}
