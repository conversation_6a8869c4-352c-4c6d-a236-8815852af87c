import { Response, NextFunction } from 'express';
import { AppResponse } from '@utils/baseResponse';
import models from '@models';

/**
 * Enhanced authentication middleware that includes license validation
 * This middleware:
 * 1. Validates the user token
 * 2. Checks if user's company has a valid license
 * 3. Ensures user is authorized to access EIR
 */
export default async function authenticateWithLicense(req: any, res: Response & AppResponse, next: NextFunction) {
    try {
        // Step 1: Validate token
        let token = req.headers.authorization;
        if (!token) {
            return res.unauthorized('Authorization token required');
        }

        const user = await models.User.findOne({ token }).populate('company');
        console.log('validate-token-with-license');
        
        if (!user) {
            return res.unauthorized('Invalid token');
        }

        req.user = user;

        // Step 2: Check if user has a company assigned
        if (!user.company) {
            return res.forbidden('User is not assigned to any company');
        }

        // Step 3: Find active subscription for the user's company
        const subscription = await models.Subscription.findOne({
            company: user.company,
            status: 'active'
        }).populate('license');

        // Check if subscription exists
        if (!subscription) {
            return res.forbidden('No active license found for your company');
        }

        // Step 4: Check if subscription is expired
        const currentDate = new Date();
        if (subscription.end_date && subscription.end_date < currentDate) {
            // Update subscription status to expired
            await models.Subscription.updateOne(
                { _id: subscription._id },
                { status: 'expired' }
            );
            return res.forbidden('Company license has expired');
        }

        // Step 5: Check if user is in the allowed users list
        const isUserAllowed = subscription.allowed_users.some(
            allowedUserId => allowedUserId.toString() === user._id.toString()
        );

        if (!isUserAllowed) {
            return res.forbidden('User is not authorized to access EIR with current company license');
        }

        // // Step 6: Check if license is active
        // if (!subscription.license.status) {
        //     return res.forbidden('License is currently inactive');
        // }

        // Add subscription info to request for potential use in controllers
        req.subscription = subscription;
        
        return next();
    } catch (error) {
        console.error('Authentication with license validation error:', error);
        return res.status(500).json({ message: 'Error during authentication' });
    }
}

/**
 * Basic authentication without license validation (for backward compatibility)
 */
export async function authenticateBasic(req: any, res: Response & AppResponse, next: NextFunction) {
    let token = req.headers.authorization;
    if (!token) return res.unauthorized();
    const user = await models.User.findOne({ token });
    console.log('validate-token-basic');
    if (!user) return res.unauthorized();
    req.user = user;
    return next();
}
