import { Request, Response, NextFunction } from 'express';
import crypto from 'crypto';

const submissionCache = new Map<string, boolean>();

const preventDuplicateSubmission = (req: Request, res: Response, next: NextFunction) => {
	const hash = crypto.createHash('sha256').update(JSON.stringify(req.body)).digest('hex');

	if (submissionCache.has(hash)) {
		return res.status(409).json({ message: 'Duplicate submission detected' });
	}

	submissionCache.set(hash, true);

	// Clear the cache after a certain period to prevent memory leaks
	setTimeout(() => {
		submissionCache.delete(hash);
	}, 60000); // 1 minute

	next();
};

export default preventDuplicateSubmission;