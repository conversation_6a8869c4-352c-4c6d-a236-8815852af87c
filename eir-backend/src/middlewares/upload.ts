import { getS3Client } from '@src/utils/s3';
import multer from 'multer';
import multerS3 from 'multer-s3';
import path from 'path';
import aws from 'aws-sdk';

const allowedFileExtensions = ['.png', '.jpg', '.gif', '.jpeg', '.xlsx', '.pdf', '.docx', '.doc'];
const allowedMimeTypes = [
	'image/png',
	'image/jpg',
	'image/jpeg',
	'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
	'application/pdf',
	'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
	'application/msword'
];

const s3 = new aws.S3({
	accessKeyId: process.env.S3_ACCESS_KEY,
	secretAccessKey: process.env.S3_SECRET_KEY,
})
const storage = (folder: string | null) =>
    multerS3({
        s3: s3,
        bucket: process.env.S3_BUCKET,
        contentType: multerS3.AUTO_CONTENT_TYPE,
        metadata: (req, file, cb) => {
            cb(null, { fieldName: file.fieldname });
        },
        key: (req, file, cb) => {
            const filename = file.originalname.replace(/[^a-zA-Z0-9.]/g, '_');
            const key = folder ? `${folder}/${filename}` : filename;
            cb(null, key);
        },
    });

const fileFilter = (req: any, file: any, callback: any) => {
	const ext = path.extname(file.originalname);
	const mimeType = file.mimetype;

	if (!allowedFileExtensions.includes(ext) || !allowedMimeTypes.includes(mimeType)) {
		return callback(null, false);
	}
	callback(null, true);
};

export const uploadFile = (folder: string | null) => {
	return (req: any, res: any, next: any) => {
		multer({
			storage: storage(folder),
			fileFilter: fileFilter,
		}).array('docs', 5)(req, res, (err: any) => {
			if (err) return next(err);
			next();
		});
	};
};
