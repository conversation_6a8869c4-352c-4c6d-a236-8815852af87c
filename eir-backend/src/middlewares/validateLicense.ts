import { Response, NextFunction } from 'express';
import { AppResponse } from '@utils/baseResponse';
import models from '@models';

/**
 * Middleware to validate user's company license
 * Checks if:
 * 1. User's company has an active subscription
 * 2. The subscription is not expired
 * 3. The user is in the allowed users list for the license
 */
export default async function validateLicense(
    req: any,
    res: Response & AppResponse,
    next: NextFunction,
) {
    try {
        // Ensure user is authenticated first
        if (!req.user) {
            return res.unauthorized('User not authenticated');
        }

        const user = req.user;

        // Check if user has a company assigned
        if (!user.company) {
            return res.forbidden('User is not assigned to any company');
        }

        // Find active subscription for the user's company
        const subscription = await models.Subscription.findOne({
            company: user.company,
            status: 'active',
        }).populate('license');

        // Check if subscription exists
        if (!subscription) {
            return res.forbidden('No active license found for your company');
        }

        // Check if subscription is expired
        const currentDate = new Date();
        if (subscription.end_date && subscription.end_date < currentDate) {
            // Update subscription status to expired
            await models.Subscription.updateOne(
                { _id: subscription._id },
                { status: 'expired' },
            );
            return res.forbidden('Company license has expired');
        }

        // Check if user is in the allowed users list
        const isUserAllowed = subscription.allowed_users.some(
            (allowedUserId) => allowedUserId.toString() === user._id.toString(),
        );

        if (!isUserAllowed) {
            return res.forbidden(
                'User is not authorized to access EIR with current company license',
            );
        }

        // Add subscription info to request for potential use in controllers
        req.subscription = subscription;

        return next();
    } catch (error) {
        console.error('License validation error:', error);
        return res.status(500).json({ message: 'Error validating license' });
    }
}

/**
 * Optional middleware that only validates license if user has a company
 * Useful for endpoints that should work for users without company assignments
 */
export async function validateLicenseOptional(
    req: any,
    res: Response & AppResponse,
    next: NextFunction,
) {
    try {
        // If user doesn't have a company, skip license validation
        if (!req.user || !req.user.company) {
            return next();
        }

        // Use the main validation logic
        return validateLicense(req, res, next);
    } catch (error) {
        console.error('Optional license validation error:', error);
        return res.status(500).json({ message: 'Error validating license' });
    }
}
