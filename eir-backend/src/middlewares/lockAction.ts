import { getRedisClient } from '@utils/redis';


export function lockAction(type) {
    return async function (req, res, next) {
        if (!req.user && !req.sessionID) return next();
        const key = `${process.env.APP_NAME}_LA_${type}_${req.user?.id ||
        req.sessionID}`;
        const count = await getRedisClient().client.incr(key);
        if (count > 1) return res.rateLimited();
        await getRedisClient().client.expire(key, 30);
        req.key = key;
        return next();
    };
};
