import models from '@models';
import configs from '@config';
import { decodeJWT } from '@utils/jwt';

export function checkJWTMiddleware(publicKey = configs.keys.user.public_key, modelName = 'User', authRequired = true) {
    return async (req, res, next) => {
        try {
            const token = req.headers.authorization && req.headers.authorization.slice(7);
            if (!token) throw new Error('invalid token!');
            if (!models[modelName]) throw new Error('model not found');
            const decoded = (await decodeJWT(token, publicKey)) as any;
            if (!decoded || !decoded.id) throw new Error('invalid payload');
            const user = await models[modelName].findById(decoded.id);
            if (!user) throw new Error('no user');
            req.user = user;
            if (user.token !== token) throw new Error('token expired.');
            return next();
        } catch (error) {
            if (authRequired) return res.unauthorized('Request unauthorized. Please login again');
            return next();
        }
    };
}
