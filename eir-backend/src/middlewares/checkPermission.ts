import * as _ from 'lodash';

export function checkPermission ({ permission = '', permissions = [] }) {
    return (req, res, next) => {
        if (!req.user) {
            return res.unauthorized('', { needLogin: true });
        }
        if (
            (permission && !req.user.permission.includes(permission)) ||
            (Array.isArray(permissions) && permissions.length &&
                _.intersection(req.user.permission, permissions).length)
        ) {
            return res.forbidden('You are not allowed to perform this action.');
        }
        return next();
    };
};
