export function checkRole({ role = '', roles = [] }) {
    return (req, res, next) => {
        if (!req.user) {
            return next();
        }
        if (
            (role && role !== req.user.role) ||
            (Array.isArray(roles) && roles.length &&
                !roles.includes(req.user.role))
        ) {
            return res.forbidden('You are not allowed to perform this action.');
        }
        return next();
    };
};
