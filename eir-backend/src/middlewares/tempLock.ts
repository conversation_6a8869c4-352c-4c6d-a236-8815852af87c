import { ERROR } from '@src/constants/error';
import { getMoment } from '@utils/datetime';

export const tempLock =
    ({ key, maxTimes = 5, lockTime = 5, message = ERROR.BROWSER_LOCKED }) =>
        async (req, res, next) => {
            console.log('req.session', req.session);
            const attemptsKey = `${key}_attempts`;
            const lockedUntilKey = `${key}_locked_until`;
            try {
                req.attemptsKey = attemptsKey;
                req.lockedUntilKey = lockedUntilKey;
                if (req.session[`${key}_locked_until`] > new Date()) throw new Error(ERROR.TEMP_LOCKED);
                req.session[attemptsKey] = (+req.session[attemptsKey] || 0) + 1;
                if (req.session[attemptsKey] >= maxTimes) {
                    req.session[`${key}_locked_until`] = getMoment().add(lockTime, 'minutes').toDate();
                    req.session[attemptsKey] = 0;
                    throw new Error(ERROR.TEMP_LOCKED);
                }

                next();
            } catch (error) {
                switch (error.message) {
                    case ERROR.TEMP_LOCKED: {
                        const msg = message || ERROR.BROWSER_LOCKED;
                        return res.badRequest(msg, {
                            info: ERROR.TEMP_LOCKED,
                            locked_until: req.session[`${key}_locked_until`],
                        });
                    }
                    default: {
                        console.error(error);
                        return res.serverInternalError(error.message);
                    }
                }
            }
        };
