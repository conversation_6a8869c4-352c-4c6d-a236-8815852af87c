import { Response, NextFunction } from 'express';
import { AppResponse } from '@utils/baseResponse';
import models from '@models';

export default async function (req: any, res: Response & AppResponse, next: NextFunction) {
    let token = req.headers.authorization;
    if (!token) return res.unauthorized();
    const user = await models.User.findOne({ token });
    console.log('validate-token');
    if (!user) return res.unauthorized();
    req.user = user;
    return next();
}