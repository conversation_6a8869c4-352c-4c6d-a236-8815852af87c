import { getRedisClient } from '@utils/redis';

import { validationResult } from 'express-validator';
import { AppRequest, AppResponse } from '@utils/baseResponse';

export function validateRequest(req: AppRequest, res: AppResponse, next: any) {
    const validationErrors = validationResult(req);
    if (!validationErrors.isEmpty()) {
        const errors = validationErrors.array();
        const msg = 'validation_failed';
        req.key && setTimeout(() => getRedisClient().client.del(req.key), 0);
        return res.badRequest(msg, errors);
    }
    return next();
}
