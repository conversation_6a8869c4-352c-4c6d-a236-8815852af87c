import config from '@config';
import { IRouteConfig } from '@src/interfaces/routes';
import baseResponse from '@utils/baseResponse';
import compression from 'compression';
import connectMongoDbSession from 'connect-mongodb-session';
import cookieParser from 'cookie-parser';
import cors from 'cors';
import express from 'express';
import session from 'express-session';
import useragent from 'express-useragent';
import helmet from 'helmet';
import hpp from 'hpp';
import http from 'http';
import morgan from 'morgan';
import initSocketEvents, { onSocketConnect, onSocketDisconnect } from './sockets';
import SocketServer from './utils/socket';
import { startCron } from './scheduler';

class App {
    public app: express.Application;
    public env: string;
    public port: string | number;
    public server: http.Server;

    constructor(routes: IRouteConfig[]) {
        this.app = express();
        this.env = config.app.env;
        this.port = config.app.port;
        this.server = http.createServer(this.app);

        this.initializeSocket();
        this.initializeMiddlewares();
        this.initializeSession();
        this.initializeRoutes(routes);
    }

    public listen() {
        this.server.listen(this.port);
        this.server.on('listening', () => {
            console.info(`=================================`);
            console.info(`======= ENV: ${this.env} =======`);
            console.info(`🚀 App listening on the port ${this.port}`);
            console.info(`=================================`);
        });

        startCron();
    }

    public getServer() {
        return this.app;
    }

    private initializeMiddlewares() {
        this.app.set('trust proxy', 1);
        this.app.use(morgan('dev'));
        this.app.use(useragent.express());
        this.app.use(cors({ origin: config.app.ORIGIN, credentials: true }));
        this.app.use(hpp());
        this.app.use(
            helmet({
                hidePoweredBy: true,
            }),
        );
        this.app.use(compression());
        this.app.use(express.json());
        this.app.use(express.urlencoded({ extended: true, limit: '200mb' }));
        this.app.use(cookieParser());
        this.app.use(baseResponse);
    }

    private initializeRoutes(routes: IRouteConfig[]) {
        routes.forEach((route) => {
            this.app.use(route.path, route.router);
        });
        this.app.get('/favicon.ico', (req, res) => res.status(204));
        // catch 404 and forward to error handler
        this.app.use(function (req, res, next) {
            console.log('req.path', req.path);
            return res.sendFile('404.html', {
                root: 'template'
            });
        });
    }

    private initializeSession() {
        const MongoDBStore = connectMongoDbSession(session);
        const store = new MongoDBStore({
            uri: config.database.uri,
            collection: 'session',
            expires: 60 * 60 * 24 * 2, 
        });

        this.app.use(
            session({
                name: config.session.name,
                secret: config.session.secret,
                cookie: {
                    path: '/',
                    httpOnly: true,
                    secure: config.app.isProd,
                    maxAge: 1000 * 60 * 60 * 24 * 7, // 
                },
                store: store,
                resave: false,
                saveUninitialized: true,
                rolling: false,
            }),
        );
    }

    private async initializeSocket() {
        await SocketServer.initSocket(this.server);
        SocketServer.bindHandlers({
            onSocketConnect,
            onSocketDisconnect,
            socketEvents: initSocketEvents(),
        });
    }
}

export default App;
