import { connect, set } from 'mongoose';
import { getRedisClient, newRedisClient, setClient } from '@utils/redis';
import { getQueueConnection } from '@utils/rabbitmq';
import configs from '@config';
import TimeoutManager from '@src/scheduler/Timeout';
import timeoutConfigs from '@src/scheduler/timeoutSetup';

export async function connectDatabase(uri = configs.database.uri, options = configs.database.options, debug = true) {
    if (debug && configs.app.env !== 'production') set('debug', true);
    set('strictQuery', true);
    await connect(uri, options).then(() => {
        console.log('📦 mongodb connected!', uri);
    });
}

export async function connectRedis(config: any = configs.redis) {
    const client = await newRedisClient(config)
    setClient(client);
}

export async function initRedisExpirePubSub(pubSubConfig: any = configs.redis) {
    const [pub, sub] = await Promise.all([newRedisClient(pubSubConfig), newRedisClient(pubSubConfig)]);
    pub.configSet('notify-keyspace-events', 'Ex');
    let isScheduler: any;
    sub.subscribe('__keyevent@0__:expired', async (key: string) => {
        const isCurrentProject = key.startsWith(configs.app.name);
        if (!isCurrentProject) return;
        isScheduler = await getRedisClient().isScheduler(key, 60);
        if (!isScheduler) return;
        try {
            await TimeoutManager.onRedisKeyExpired(key);
        } catch (error) {
            console.error('EXPIRE EVENT ERROR', key);
        } finally {
            isScheduler && (await getRedisClient().client.del(isScheduler));
        }
    });
}


export default async function initService({
    mongo = true,
    redis = true,
    mongoOptions,
    rabbitmq = false,
    redisOptions,
    onReadyFns = [],
}: {
    mongo?: boolean;
    redis?: boolean;
    rabbitmq?: boolean;
    mongoOptions?: { uri?: string; options?: any; debug?: boolean };
    redisOptions?: { expireEvent?: boolean; config?: { url: string; password?: string; database?: number } };
    onReadyFns?: Array<(...args: any[]) => any>;
} = {}) {
    mongoOptions = {
        debug: mongoOptions?.debug ?? true,
        options: mongoOptions?.options ?? configs.database.options,
        uri: mongoOptions?.uri ?? configs.database.uri,
    };
    redisOptions = {
        expireEvent: redisOptions?.expireEvent ?? false,
        config: { ...configs.redis, ...(redisOptions?.config || {}) },
    };

    if (mongo) await connectDatabase(mongoOptions.uri, mongoOptions.options, mongoOptions.debug);
    if (redis) await connectRedis(redisOptions.config);
    if (redisOptions.expireEvent) {
        TimeoutManager.setupTimeouts(timeoutConfigs);
        await initRedisExpirePubSub(redisOptions.config);
    }
    if (rabbitmq) await getQueueConnection();
    await Promise.all(onReadyFns.map((fn) => fn()));
}
