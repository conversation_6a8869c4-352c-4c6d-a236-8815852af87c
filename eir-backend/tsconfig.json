{"compileOnSave": false, "compilerOptions": {"target": "es2017", "lib": ["es2017", "esnext.asynciterable"], "typeRoots": ["node_modules/@types"], "allowSyntheticDefaultImports": true, "experimentalDecorators": true, "emitDecoratorMetadata": true, "forceConsistentCasingInFileNames": true, "moduleResolution": "node", "module": "commonjs", "pretty": true, "sourceMap": true, "outDir": "dist", "allowJs": true, "noEmit": false, "esModuleInterop": true, "resolveJsonModule": true, "importHelpers": true, "baseUrl": "src", "paths": {"@src/*": ["*"], "@config": ["config"], "@constants/*": ["constants/*"], "@socket/*": ["socket/*"], "@controllers/*": ["controllers/*"], "@databases": ["databases"], "@dtos/*": ["dtos/*"], "@exceptions/*": ["exceptions/*"], "@interfaces/*": ["interfaces/*"], "@middlewares/*": ["middlewares/*"], "@models": ["models"], "@models/*": ["models/*"], "@routes/*": ["routes/*"], "@services/*": ["services/*"], "@utils/*": ["utils/*"], "@scripts/*": ["scripts/*"]}}, "include": ["src/**/*.ts", "src/**/*.json", ".env"], "exclude": ["node_modules", "src/http", "src/logs"]}