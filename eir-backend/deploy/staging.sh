#!/usr/bin/env bash

SERVER="future_tech"
DEPLOY_PATH="/var/www/EIR/backend"
SOURCE_FOLDER_NAME="dist"
PROJECT_NAME=eir-backend

FILE_NAME="$PROJECT_NAME.zip"
echo "=================================="
yarn build
cp package.prod.json dist/package.json
cp ecosystem.config.js dist/ecosystem.config.js
cp .env.staging dist/env.staging
zip -r1 ${FILE_NAME} ${SOURCE_FOLDER_NAME}
echo "=================================="
echo "Upload source..."
scp ${FILE_NAME} ${SERVER}:~
echo "=================================="
echo "Deploying..."

SCRIPT1="cd ~ && sudo mkdir -p $DEPLOY_PATH"
SCRIPT2="unzip $FILE_NAME -d $PROJECT_NAME"
SCRIPT3="sudo cp -rf $PROJECT_NAME/$SOURCE_FOLDER_NAME/* $DEPLOY_PATH"
SCRIPT4="sudo rm $FILE_NAME && sudo rm -rf $PROJECT_NAME"
SCRIPT5="cd $DEPLOY_PATH && sudo mv env.staging .env.staging"
# shellcheck disable=SC2029
SCRIPT6="cd $DEPLOY_PATH && pm2 restart eir-api && pm2 logs eir-api"
ssh ${SERVER} "$SCRIPT1 && $SCRIPT2 && $SCRIPT3 && $SCRIPT4 && $SCRIPT5 && $SCRIPT6"

echo "Deploy finish..."
echo "=================================="
rm -rf ${FILE_NAME}

echo "\n=================================="
echo "All Finish... `date`"
