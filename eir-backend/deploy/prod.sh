#!/usr/bin/env bash

SERVER="cdas_prod_ets_app_0"
SERVER1="cdas_prod_ets_app_1"
DEPLOY_PATH="/home/<USER>/EIR/backend"
SOURCE_FOLDER_NAME="dist"
PROJECT_NAME=eir-backend

FILE_NAME="$PROJECT_NAME.zip"
echo "==================================" 
yarn build
cp package.uat.json dist/package.json
cp ecosystem.config.js dist/ecosystem.config.js
cp .env.prod dist/env.prod
ls dist/
zip -r1 ${FILE_NAME} ${SOURCE_FOLDER_NAME}
echo "=================================="
echo "Upload source..."
scp ${FILE_NAME} ${SERVER}:~
scp ${FILE_NAME} ${SERVER1}:~
echo "=================================="
echo "Deploying..."

SCRIPT1="cd ~ && sudo mkdir -p $DEPLOY_PATH && pwd && ls -la" 
SCRIPT2="unzip -o $FILE_NAME -d $PROJECT_NAME"
SCRIPT3="sudo cp -rf $PROJECT_NAME/$SOURCE_FOLDER_NAME/* $DEPLOY_PATH"
SCRIPT4="sudo rm $FILE_NAME && sudo rm -rf $PROJECT_NAME"
SCRIPT5="cd $DEPLOY_PATH && pwd && sudo mv env.prod .env.prod"
# shellcheck disable=SC2029
SCRIPT6="cd $DEPLOY_PATH && pm2 restart eir-api"

ssh ${SERVER} "$SCRIPT1 && $SCRIPT2 && $SCRIPT3 && $SCRIPT4 && $SCRIPT5 && $SCRIPT6"
ssh ${SERVER1} "$SCRIPT1 && $SCRIPT2 && $SCRIPT3 && $SCRIPT4 && $SCRIPT5 && $SCRIPT6"

echo "Deploy finish..."
echo "=================================="
rm -rf ${FILE_NAME}

ssh ${SERVER} "cd $DEPLOY_PATH && pm2 logs eir-api"

echo "\n=================================="
echo "All Finish... `date`"
