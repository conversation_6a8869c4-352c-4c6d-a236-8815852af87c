# EIR Backend Development Guidelines

## Project Overview
This is a TypeScript + Express + MongoDB + Mongoose API server for the EIR (Equipment Interchange Receipt) system. The project follows a modular architecture with clear separation of concerns.

## Project Structure

```
src/
├── app.ts                 # Express app configuration
├── server.ts             # Server entry point
├── bootstrap/            # Application initialization
├── config/               # Configuration management
├── constants/            # Application constants
├── helpers/              # Utility helper functions
├── interfaces/           # TypeScript interfaces
├── middlewares/          # Express middlewares
├── models/               # Mongoose models
├── routes/               # API routes (mobile & portal)
├── scheduler/            # Cron jobs and scheduled tasks
├── sockets/              # Socket.io handlers
├── utils/                # Utility functions
└── validation/           # Request validation schemas
```

## Core Development Rules

### 1. API Response Format

**ALL API responses must follow the standardized format:**

```typescript
// Success Response
{
    "status": "success",
    "data": any,
    "error_message": "",
    "error_code": ""
}

// Error Response
{
    "status": "error", 
    "data": null,
    "error_message": string,
    "error_code": string | number
}
```

**Use the provided response helpers:**
- `res.ok(data)` - 200 success
- `res.badRequest(message, data)` - 400 bad request
- `res.unauthorized(message, data)` - 401 unauthorized
- `res.forbidden(message, data)` - 403 forbidden
- `res.notFound(message, data)` - 404 not found
- `res.serverInternalError(message, data)` - 500 server error

### 2. Route Structure

**Routes are organized by platform:**
- `src/routes/mobile/` - Mobile app endpoints
- `src/routes/portal/` - Web portal endpoints

**Each route file must export:**
```typescript
export const authRoute: Route[] = []; // Protected routes
export const publicRoute: Route[] = []; // Public routes
```

**Route definition pattern:**
```typescript
{
    method: 'get' | 'post' | 'patch' | 'put' | 'delete',
    path: '/endpoint-path',
    cb: controllers.handlerFunction,
    middleware: [validation, authentication, etc.]
}
```

### 3. Controller Implementation

**Controllers must:**
- Use `matchedData(req)` to extract validated request data
- Handle errors with try-catch blocks
- Return appropriate response using response helpers
- Follow async/await pattern

**Controller template:**
```typescript
export async function controllerName(req, res) {
    try {
        const { param1, param2 } = matchedData(req);
        
        // Business logic here
        const result = await models.ModelName.find(query);
        
        return res.ok(result);
    } catch (error) {
        console.error('Error in controllerName:', error);
        return res.serverInternalError('Error message');
    }
}
```

### 4. Request Validation

**All request parameters must be validated using express-validator:**

```typescript
import * as commonValidations from '@src/validation/common';
import { validateRequest } from '@src/middlewares';

// In route middleware array:
middleware: [
    commonValidations.string('fieldName'),
    commonValidations.number('numericField').optional(),
    commonValidations.array('arrayField', { required: true }),
    validateRequest, // Must be last validation middleware
]
```

**Available validation helpers:**
- `string(field, maxLength)` - String validation
- `number(field)` - Numeric validation
- `boolean(field)` - Boolean validation
- `array(field, options)` - Array validation
- `objectId(field)` - MongoDB ObjectId validation
- `ISODate(field)` - ISO date validation

### 5. Model Definitions

**Models must:**
- Use TypeScript interfaces from `@interfaces/models`
- Include proper schema validation
- Use consistent naming conventions
- Include timestamps with custom field names

**Model template:**
```typescript
import { Document, Model, model, Schema } from 'mongoose';
import { IModelName } from '@interfaces/models';

const modelSchema = new Schema<IModelName>(
    {
        field1: String,
        field2: {
            type: Schema.Types.ObjectId,
            ref: 'ReferencedModel',
        },
    },
    {
        timestamps: { createdAt: 'created_at', updatedAt: 'updated_at' },
        versionKey: false,
    },
);

interface IModelNameModel extends Model<IModelName> {}

export default model<IModelName & Document, IModelNameModel>('ModelName', modelSchema);
```

### 6. Authentication & Authorization

**Protected routes must use appropriate middleware:**
- `authenticate` - Basic token authentication
- `authenticateWithLicense` - License-based authentication
- `checkRole` - Role-based access control
- `checkPermission` - Permission-based access control

### 7. Error Handling

**Error handling standards:**
- Always use try-catch in async controllers
- Log errors with descriptive messages
- Never expose internal errors in production
- Use appropriate HTTP status codes
- Provide meaningful error messages to clients

### 8. Database Queries

**Query patterns:**
- Use `lean()` for read-only operations
- Implement pagination with `limit()` and `skip()`
- Use `populate()` for referenced documents
- Always handle query errors

**Pagination template:**
```typescript
const { limit = 10, page = 1 } = matchedData(req);
const skip = (page - 1) * limit;

const [items, total] = await Promise.all([
    models.ModelName.find(query)
        .populate('reference', 'fields')
        .sort({ created_at: -1 })
        .skip(skip)
        .limit(limit)
        .lean(),
    models.ModelName.countDocuments(query),
]);

return res.ok({
    items,
    pagination: {
        total,
        page,
        limit,
        totalPages: Math.ceil(total / limit),
    },
});
```

### 9. TypeScript Usage

**TypeScript requirements:**
- Use proper type definitions for all functions
- Import interfaces from `@interfaces/`
- Use path aliases defined in tsconfig.json
- Avoid `any` type when possible

**Common imports:**
```typescript
import { AppRequest, AppResponse } from '@utils/baseResponse';
import { matchedData } from 'express-validator';
import models from '@models';
```

### 10. File Naming Conventions

**Naming standards:**
- Routes: `entity.route.ts`
- Controllers: `entity.controller.ts`
- Models: `entity.model.ts`
- Interfaces: `descriptive-name.ts`
- Utilities: `descriptive-name.ts`
- Use kebab-case for file names
- Use PascalCase for model names
- Use camelCase for function names

### 11. Environment Configuration

**Configuration management:**
- All environment variables in `src/config/index.ts`
- Use environment-specific .env files
- Never hardcode sensitive values
- Provide sensible defaults where appropriate

### 12. Middleware Usage

**Common middleware patterns:**
- Request validation must come before business logic
- Authentication before authorization
- Use `validateRequest` as the last validation middleware
- Custom middleware should follow Express patterns

### 13. Data Normalization

**Response data should be normalized using utility functions:**
- Use `normalizeUserProfile()` for user data
- Create similar normalize functions for other entities
- Remove sensitive fields before sending responses
- Use `lodash.pick()` to select specific fields

**Example:**
```typescript
import { normalizeUserProfile } from '@src/routes/normalize';

const users = await models.User.find(query).lean();
return res.ok({
    items: users.map(user => normalizeUserProfile(user)),
    total,
});
```

### 14. Socket.io Integration

**Socket events must:**
- Be defined in `src/sockets/handlers/`
- Use the `socketRes` helper for responses
- Follow the same response format as HTTP APIs
- Handle authentication appropriately

### 15. License Management (EIR Specific)

**For EIR system features:**
- Always validate license status for company operations
- Check user access lists in subscriptions
- Verify license expiry dates
- Use `authenticateWithLicense` middleware for protected operations

### 16. Search Implementation

**Search functionality patterns:**
```typescript
const { search = '', limit = 10, page = 1 } = req.query;
const query: any = {};

if (search) {
    query.$or = [
        { field1: { $regex: search.trim(), $options: 'i' } },
        { field2: { $regex: search.trim(), $options: 'i' } },
    ];
}

// Add additional filters
if (req.query.filter) {
    query.filterField = req.query.filter;
}
```

### 17. File Upload Handling

**File uploads must:**
- Use multer middleware for file handling
- Validate file types and sizes
- Store files in AWS S3 using the configured service
- Return file URLs in responses

### 18. Logging Standards

**Logging requirements:**
- Use console.error for error logging
- Include descriptive context in log messages
- Log important business operations
- Never log sensitive information

### 19. Testing Guidelines

**When implementing new features:**
- Write unit tests for controllers
- Test validation middleware
- Test error scenarios
- Verify response formats match standards

### 20. Code Review Checklist

**Before submitting code:**
- [ ] Follows response format standards
- [ ] Includes proper error handling
- [ ] Uses TypeScript types correctly
- [ ] Implements request validation
- [ ] Follows naming conventions
- [ ] Includes appropriate middleware
- [ ] Handles authentication/authorization
- [ ] Uses proper database query patterns
- [ ] Normalizes response data
- [ ] Includes descriptive error messages

## Quick Reference

### Common Imports
```typescript
import { AppRequest, AppResponse } from '@utils/baseResponse';
import { matchedData } from 'express-validator';
import models from '@models';
import * as commonValidations from '@src/validation/common';
import { validateRequest } from '@src/middlewares';
```

### Response Helpers
```typescript
res.ok(data)                           // 200 Success
res.badRequest(message, data)          // 400 Bad Request
res.unauthorized(message, data)        // 401 Unauthorized
res.forbidden(message, data)           // 403 Forbidden
res.notFound(message, data)            // 404 Not Found
res.serverInternalError(message, data) // 500 Server Error
```

### Validation Helpers
```typescript
commonValidations.string('field', maxLength)
commonValidations.number('field')
commonValidations.boolean('field')
commonValidations.array('field', options)
commonValidations.objectId('field')
commonValidations.ISODate('field')
```

This documentation ensures consistency across the EIR backend codebase and provides clear guidelines for implementing new features while maintaining code quality and architectural patterns.
