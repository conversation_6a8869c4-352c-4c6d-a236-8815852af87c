# Subscription User Management - PATCH Method

## Update Users in Subscription (Replace entire list)

### Set specific users
```bash
curl -X PATCH "http://localhost:3000/api/portal/subscription/SUBSCRIPTION_ID/users" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_AUTH_TOKEN" \
  -d '{
    "allowed_users": ["USER_ID_1", "USER_ID_2", "USER_ID_3"]
  }'
```

### Add users (by including existing + new users)
```bash
# First get current subscription to see existing users
curl -X GET "http://localhost:3000/api/portal/subscription/SUBSCRIPTION_ID" \
  -H "Authorization: Bearer YOUR_AUTH_TOKEN"

# Then update with existing + new users
curl -X PATCH "http://localhost:3000/api/portal/subscription/SUBSCRIPTION_ID/users" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_AUTH_TOKEN" \
  -d '{
    "allowed_users": ["EXISTING_USER_1", "EXISTING_USER_2", "NEW_USER_1", "NEW_USER_2"]
  }'
```

### Remove users (by excluding them from the list)
```bash
# Update with only the users you want to keep
curl -X PATCH "http://localhost:3000/api/portal/subscription/SUBSCRIPTION_ID/users" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_AUTH_TOKEN" \
  -d '{
    "allowed_users": ["USER_TO_KEEP_1", "USER_TO_KEEP_2"]
  }'
```

### Clear all users
```bash
curl -X PATCH "http://localhost:3000/api/portal/subscription/SUBSCRIPTION_ID/users" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_AUTH_TOKEN" \
  -d '{
    "allowed_users": []
  }'
```

## Benefits of PATCH Method:
1. **Atomic Operation**: Single request to set the exact user list
2. **Simpler Logic**: No need to track additions/removals separately  
3. **Idempotent**: Same request produces same result
4. **Flexible**: Can add, remove, or replace users in one operation
5. **Clear State**: Always know the exact final state of allowed_users

## Response Format:
```json
{
  "status": "success",
  "data": {
    "_id": "subscription_id",
    "company": {
      "_id": "company_id", 
      "name": "Company Name"
    },
    "license": {
      "_id": "license_id",
      "name": "License Name",
      "status": true
    },
    "allowed_users": [
      {
        "_id": "user_id_1",
        "username": "user1",
        "fullname": "User One",
        "email": "<EMAIL>"
      }
    ],
    "limit_container": 100,
    "end_date": "2025-12-31T23:59:59.999Z",
    "status": "active"
  }
}
```
