# EIR Backend - Controllers & Services Inconsistency Analysis

## Summary
This document identifies all controllers and services that don't follow the established development guidelines for consistency.

## Major Inconsistencies Found

### 1. Missing Error Handling (Try-Catch Blocks)

**❌ Controllers without proper error handling:**

#### Portal Controllers:
- `src/routes/portal/controllers/group.controller.ts` - `listGroup()`
- `src/routes/portal/controllers/container.controller.ts` - `listContainer()`
- `src/routes/portal/controllers/journey.controller.ts` - `listJourney()`
- `src/routes/portal/controllers/photo.controller.ts` - `getPhotos()`
- `src/routes/portal/controllers/damage.controller.ts` - `getDamages()`
- `src/routes/portal/controllers/entity.controller.ts` - `listEntity()`, `createEntity()`

#### Mobile Controllers:
- `src/routes/mobile/controllers/user.controller.ts` - `profile()`
- `src/routes/mobile/controllers/entity.controller.ts` - `createEntity()`

### 2. Not Using `matchedData(req)` for Request Validation

**❌ Controllers using `req.query` or `req.body` directly:**

#### Portal Controllers:
- `src/routes/portal/controllers/group.controller.ts` - Uses `req.query` directly
- `src/routes/portal/controllers/container.controller.ts` - Uses `req.query` directly
- `src/routes/portal/controllers/journey.controller.ts` - Uses `req.query` directly
- `src/routes/portal/controllers/photo.controller.ts` - Uses `req.query` directly
- `src/routes/portal/controllers/damage.controller.ts` - Uses `req.query` directly

#### Mobile Controllers:
- `src/routes/mobile/controllers/container.controller.ts` - `filterContainer()` uses `req.query`

### 3. Inconsistent Response Format

**❌ Controllers with non-standard response patterns:**

#### Content Route (Mobile):
- `src/routes/mobile/content.route.ts` - Controllers defined inline in route file instead of separate controller file
- Functions `_getContent()`, `_getAll()`, `_updateContent()` should be in separate controller file

### 4. Missing Request Validation

**❌ Routes without proper validation middleware:**

#### Portal Routes:
- `src/routes/portal/controllers/group.controller.ts` - No validation for query parameters
- `src/routes/portal/controllers/container.controller.ts` - No validation for query parameters
- `src/routes/portal/controllers/journey.controller.ts` - No validation for query parameters
- `src/routes/portal/controllers/photo.controller.ts` - No validation for query parameters
- `src/routes/portal/controllers/damage.controller.ts` - No validation for query parameters

### 5. Inconsistent Error Logging

**❌ Controllers with inconsistent error logging:**

#### Portal Controllers:
- `src/routes/portal/controllers/group.controller.ts` - No error logging
- `src/routes/portal/controllers/container.controller.ts` - No error logging
- `src/routes/portal/controllers/journey.controller.ts` - No error logging

#### Mobile Controllers:
- `src/routes/mobile/content.route.ts` - Uses `console.log` instead of `console.error`

### 6. Service Layer Issues

**❌ Services not following patterns:**

#### Portal Services:
- `src/routes/portal/service/ctr.service.ts` - `getRoleAcl()` uses individual updates instead of bulk operations
- `src/routes/portal/service/group.service.ts` - Inconsistent with other service patterns
- Services don't return standardized responses
- Services don't have proper error handling patterns

#### Mobile Services:
- `src/routes/mobile/services/receipt.service.ts` - Some functions don't handle errors properly
- `src/routes/mobile/services/damage.service.ts` - Missing error handling in some functions

### 7. TypeScript Issues

**❌ Controllers with TypeScript inconsistencies:**

#### Portal Controllers:
- `src/routes/portal/controllers/entity.controller.ts` - Uses `any` types extensively
- `src/routes/portal/controllers/third-party.controller.ts` - Missing proper type definitions

#### Mobile Controllers:
- `src/routes/mobile/controllers/photo.controller.ts` - Uses `any` types
- `src/routes/mobile/controllers/receipt.controller.ts` - Uses `any` types extensively

### 8. File Organization Issues

**❌ Files not following naming conventions:**

#### Content Route:
- `src/routes/mobile/content.route.ts` - Controllers should be in separate `controllers/content.controller.ts` file

### 9. Inconsistent Pagination Implementation

**❌ Controllers with non-standard pagination:**

#### Portal Controllers:
- `src/routes/portal/controllers/group.controller.ts` - Missing totalPages calculation
- `src/routes/portal/controllers/container.controller.ts` - Missing totalPages calculation
- `src/routes/portal/controllers/journey.controller.ts` - Missing totalPages calculation
- `src/routes/portal/controllers/photo.controller.ts` - Missing totalPages calculation
- `src/routes/portal/controllers/damage.controller.ts` - Missing totalPages calculation

### 10. Missing Data Normalization

**❌ Controllers not normalizing response data:**

#### Portal Controllers:
- `src/routes/portal/controllers/group.controller.ts` - Returns raw database objects
- `src/routes/portal/controllers/container.controller.ts` - Returns raw database objects
- `src/routes/portal/controllers/journey.controller.ts` - Returns raw database objects

## Detailed Fix Requirements

### High Priority Fixes:

1. **Add try-catch blocks** to all controllers missing error handling
2. **Replace `req.query`/`req.body`** with `matchedData(req)` in all controllers
3. **Add request validation** to all routes missing validation middleware
4. **Move inline controllers** to separate controller files
5. **Standardize error logging** using `console.error` with descriptive messages

### Medium Priority Fixes:

1. **Add totalPages calculation** to pagination responses
2. **Implement data normalization** functions for response data
3. **Improve TypeScript types** by removing `any` types
4. **Standardize service layer** error handling and response patterns

### Low Priority Fixes:

1. **Optimize database queries** in service layer
2. **Add consistent logging** throughout service functions
3. **Implement proper return types** for service functions

## Recommended Action Plan

1. **Phase 1**: Fix error handling and request validation (High Priority)
2. **Phase 2**: Standardize response formats and pagination (Medium Priority)  
3. **Phase 3**: Improve TypeScript types and service patterns (Low Priority)

## Specific Fix Examples

### Example 1: Fix Missing Error Handling

**❌ Current (group.controller.ts):**
```typescript
export async function listGroup(req, res) {
    await getGroup();
    const { search = '', level, limit = 10, page = 1 } = req.query;
    // ... rest of function without try-catch
}
```

**✅ Should be:**
```typescript
export async function listGroup(req, res) {
    try {
        await getGroup();
        const { search = '', level, limit = 10, page = 1 } = matchedData(req);
        // ... rest of function
        return res.ok({ items: companies, total, page: +page, limit: +limit });
    } catch (error) {
        console.error('Error listing groups:', error);
        return res.serverInternalError('Error retrieving groups');
    }
}
```

### Example 2: Fix Request Validation

**❌ Current (container.controller.ts):**
```typescript
export async function listContainer(req, res) {
    const { search = '', limit = 10, page = 1 } = req.query;
    // No validation middleware in route
}
```

**✅ Should be:**
```typescript
// In route file:
middleware: [
    commonValidations.string('search').optional(),
    commonValidations.number('limit').optional(),
    commonValidations.number('page').optional(),
    validateRequest,
]

// In controller:
export async function listContainer(req, res) {
    try {
        const { search = '', limit = 10, page = 1 } = matchedData(req);
        // ... rest of function
    } catch (error) {
        console.error('Error listing containers:', error);
        return res.serverInternalError('Error retrieving containers');
    }
}
```

### Example 3: Fix Pagination Response

**❌ Current:**
```typescript
return res.ok({ items: companies, total, page: +page, limit: +limit });
```

**✅ Should be:**
```typescript
return res.ok({
    items: companies,
    pagination: {
        total,
        page: +page,
        limit: +limit,
        totalPages: Math.ceil(total / limit),
    },
});
```

### Example 4: Fix Inline Controllers

**❌ Current (content.route.ts):**
```typescript
// Controllers defined in route file
async function _getContent(req, res) {
    try {
        const { lang } = matchedData(req);
        // ... function body
    } catch (error) {
        console.log('ERROR | Content _getContent', error);
        return res.serverInternalError(error.message);
    }
}
```

**✅ Should be:**
```typescript
// Create src/routes/mobile/controllers/content.controller.ts
export async function getContent(req, res) {
    try {
        const { lang } = matchedData(req);
        // ... function body
    } catch (error) {
        console.error('Error getting content:', error);
        return res.serverInternalError('Error retrieving content');
    }
}

// In route file:
import * as controllers from './controllers/content.controller';
cb: controllers.getContent,
```

## Files Requiring Immediate Attention

### Critical (Must Fix):
1. `src/routes/portal/controllers/group.controller.ts`
2. `src/routes/portal/controllers/container.controller.ts`
3. `src/routes/portal/controllers/journey.controller.ts`
4. `src/routes/portal/controllers/photo.controller.ts`
5. `src/routes/portal/controllers/damage.controller.ts`
6. `src/routes/mobile/content.route.ts`

### Important (Should Fix):
1. `src/routes/portal/controllers/entity.controller.ts`
2. `src/routes/mobile/controllers/entity.controller.ts`
3. `src/routes/mobile/controllers/container.controller.ts`
4. `src/routes/portal/service/ctr.service.ts`

Each controller should be updated to follow the established patterns in `DEVELOPMENT_GUIDELINES.md`.
